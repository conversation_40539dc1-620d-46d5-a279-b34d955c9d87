﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Library.Logs.Services;
using Library.SlackService.Interfaces;
using Library.SlackService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SlackNet.Extensions.DependencyInjection;

namespace Libraries.Abstractions.Processors
{
    public static class TriggeredProcessorExternsions
    {
        public static void AddTriggeredProcessorBaseDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            const string SlackTokenKey = "Slack:Token";
            services.AddScoped(s => new FileLogger(initializeManually: true));
            services.AddHttpClient();
            services.AddSlackNet(c => {
                c.UseApiToken(configuration.GetValue<string>(SlackTokenKey));
            });
            services.AddScoped<ISlackBotService, SlackBotService>();
            services.AddScoped<DirectSlackLogHelper>();
        }
    }
}
