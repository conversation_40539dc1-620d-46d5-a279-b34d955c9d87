using HCCBSchemeSnapshotProcessor.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;

namespace HCCBSchemeSnapshotProcessor
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((hostingContext, configBuilder) =>
                {
                    configBuilder
                        .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                        .AddEnvironmentVariables();
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddAzureStorageQueues(c =>
                    {
                        // Configure to process only one item at a time
                        c.BatchSize = 1;
                        c.MaxPollingInterval = TimeSpan.FromSeconds(10);
                        c.VisibilityTimeout = TimeSpan.FromMinutes(60); // Long timeout for processing
                        c.MaxDequeueCount = 3;
                    });
                })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseSerilog((context, loggerConfiguration) =>
                {
                    loggerConfiguration
                        .ReadFrom.Configuration(context.Configuration)
                        .WriteTo.Console(Serilog.Events.LogEventLevel.Information);
                })
                .UseConsoleLifetime();

            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }
    }
}
