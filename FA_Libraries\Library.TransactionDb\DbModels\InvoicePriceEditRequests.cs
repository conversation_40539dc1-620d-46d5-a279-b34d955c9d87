﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Library.TransactionDb.DbModels
{
    public class InvoicePriceEditRequests
    {
        public long Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastUpdatedAt { get; set; }
        public long CompanyId { get; set; }
        public long OutletId { get; set; }
        public long EmployeeId { get; set; }
        public long DistributorId { get; set; }
        public Guid SessionId { get; set; }
        public string InvoiceNumber { get; set; }
        public PriceEditApprovalStatus ApprovalStatus { get; set; }
        public long? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string UpdationContext { get; set; }
        public virtual List<InvoicePriceEditRequestItems> Items { get; set; }
    }

    public class InvoicePriceEditRequestItems
    {
        public long Id { get; set; }
        public long InvoicePriceEditRequestsId { get; set; }
        public long ProductId { get; set; }
        public decimal OldPrice { get; set; }
        public decimal NewPrice { get; set; }

        public virtual InvoicePriceEditRequests InvoicePriceEditRequests { get; set; }
    }

    public enum PriceEditApprovalStatus
    {
        Pending,
        Approved,
        Rejected
    }
}
