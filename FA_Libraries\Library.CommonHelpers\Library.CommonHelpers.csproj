﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    <PackageReference Include="Azure.Identity" Version="1.15.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.AzureKeyVault" Version="3.1.24" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.76.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="prometheus-net" Version="8.2.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.8" />
    <PackageReference Include="Opw.HttpExceptions" Version="4.0.3" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\fa_dotnet_core\FA.Cache\FA.Cache.csproj" />
    <ProjectReference Include="..\fa_dotnet_logger\FA.Logger\FA.Logger.csproj" />
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
  </ItemGroup>
</Project>
