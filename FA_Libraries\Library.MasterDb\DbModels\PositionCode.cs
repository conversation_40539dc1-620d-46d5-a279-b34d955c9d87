﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    [Table("PositionCodes")]
    public class PositionCode
    {
        public PositionCode()
        {
            PositionCodeEntityMappings = new HashSet<PositionCodeEntityMapping>();
        }

        public long Id { get; set; }

        public long CompanyId { get; set; }

        public string CodeId { get; set; }

        public string Name { get; set; }

        public PositionCodeLevel Level { get; set; }

        public long? ParentId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public bool Deleted { get; set; }

        public PositionCode Parent { get; set; }

        public ICollection<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }

        public ICollection<PositionBeatMapping> PositionBeatMappings { get; set; }

        public ICollection<PositionDistributorMapping> PositionDistributorMappings { get; set; }

        public ICollection<RoutePositionMapping> RoutePositionsMappings { get; set; }

        [NotMapped]
        public long EntityId { get; set; }

        public UserType PositionType { get; set; }

        public PositionCode ShallowCopy()
        {
            return (PositionCode)MemberwiseClone();
        }
    }
}
