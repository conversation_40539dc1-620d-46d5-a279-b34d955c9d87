﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("CompanyWiseGTFeatureMetrics")]
    public class CompanyWiseGTFeatureMetric
    {
        [Key]
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long GTFeatureMetricId { get; set; }
        public bool IsDeleted { get; set; } = false;
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        [ForeignKey("GTFeatureMetricId")]
        public virtual GTFeatureMetric GTFeatureMetric { get; set; }
    }
}
