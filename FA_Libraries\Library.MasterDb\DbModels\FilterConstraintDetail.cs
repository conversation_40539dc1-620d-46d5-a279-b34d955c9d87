﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    [Table("FilterConstraintDetails")]
    public class FilterConstraintDetail
    {
        [Key]
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("EntityType")]
        public FilterConstraintEntityType EntityType { get; set; }

        [Column("ConstraintJson")]
        public string ConstraintJson { get; set; }

        public UserPlatform? UserPlatform { get; set; }

        public bool IsDeactive { get; set; }
    }
}
