﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels;

public class LocationWithMarginSlab
{
    public long Id { get; set; }

    public string GUID { get; set; }

    public string Slab { get; set; }

    public MarginEntity EntityType { get; set; }

    public Dictionary<long, double> EntityMargins { get; set; }

    public MarginValueType ValueType { get; set; }

    public long? EntityMarginSlabId { get; set; }

    [Column("MarginDescription")]
    public string MarginDescription { get; set; }
}

public class LocationWithMarginSlabWithoutMargins
{
    public long Id { get; set; }

    public string GUID { get; set; }

    public string Slab { get; set; }

    public MarginEntity EntityType { get; set; }

    public MarginValueType ValueType { get; set; }

    public long? EntityMarginSlabId { get; set; }

    public long? BeatRouteId { get; set; }

    public List<long?> MappedBeatRouteIds { get; set; }
}
