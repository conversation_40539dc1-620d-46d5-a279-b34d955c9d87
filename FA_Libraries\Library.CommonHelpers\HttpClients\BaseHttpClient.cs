﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Library.CommonHelpers.HttpClients
{
    public abstract class BaseHttpClient
    {
        protected readonly System.Net.Http.HttpClient Client;

        protected BaseHttpClient(System.Net.Http.HttpClient httpClient)
        {
            Client = httpClient;
        }

        public virtual Task<HttpResponseMessage> GetAsync(string endpoint, CancellationToken token = default)
        {
            return Client.GetAsync(endpoint, token);
        }

        public virtual Task<HttpResponseMessage> PostAsync(string endpoint, ByteArrayContent byteContent, CancellationToken token = default)
        {
            return Client.PostAsync(endpoint, byteContent, token);
        }
    }
}
