﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum OrderKind
{
    [Display(Name = "Orders")] OtherOrder = 0,

    [Display(Name = "First Order at Shops")]
    FirstOrder = 1,

    [Display(Name = "Retailer Target vs Achievment on Order")]
    TargetVsAchievment = 2,

    [Display(Name = "OTP at New Outlet Creation")]
    NewOutletCreation = 3,

    [Display(Name = "Activation Code")] ActivationCode = 4,

    [Display(Name = "On order confirmation")]
    OnOrderConfirmation = 5,

    [Display(Name = "On earning coupon scheme")]
    OnEarningCoupons = 6,

    [Display(Name = "Validation OTP at New Outlet Creation")]
    NewOutletCreationValidation = 7,

    [Display(Name = "Pending Order")]
    PendingOrder = 8,

    [Display(Name = "Accepted")]
    Accepted = 9,

    [Display(Name = "Rejected")]
    Rejected = 10,

    [Display(Name = "Auto Closed")]
    AutoClosed = 11,

    [Display(Name = "Cancelled")]
    Cancelled = 12,

    [Display(Name = "Invoiced")]
    Invoiced = 13,

    [Display(Name = "Partial")]
    Partial = 14,

    [Display(Name = "Pending For Cancellation")]
    PendingForCancellation = 15,

    #region New Approval  
    [Display(Name = "Manager Notification")]
    OutletManagerNotification = 16,

    [Display(Name = "Outlet Approved")]
    OutletApproved = 17,

    [Display(Name = "Outlet Rejected")]
    OutletRejected = 18,

    [Display(Name = "Outlet Created")]
    OutletCreated = 19
    #endregion
}
