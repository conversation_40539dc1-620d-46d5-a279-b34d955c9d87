﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    public class Product
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public double Price { get; set; }

        public string DisplayCategory { get; set; }

        public long? ProductDisplayCategoryId { get; set; }

        public int? DisplayOrder { get; set; }

        public bool IsPromoted { get; set; }

        public string Unit { get; set; }

        [Column("ProductCategoryId")]
        public long? ProductCategoryId { get; set; }

        public string ImageId { get; set; }

        public string VariantName { get; set; }

        [Column("MRPNumeric")]
        public decimal MRP { get; set; }

        [Column("MRP")]
        public string DisplayMRP { get; set; }

        public double StandardUnitConversionFactor { get; set; }

        public string PackSize { get; set; }

        public DateTime LastUpdatedAt { get; internal set; }

        public bool IsActive { get; set; }

        public bool Deleted { get; set; }

        public long CompanyId { get; set; }

        public bool IsTopSelling { get; set; }

        public string LocalName { get; set; }

        public string Schemes { get; set; }

        public bool IsFocused { get; set; }

        public decimal? PricePlacement { get; set; }

        public decimal? PriceRegular { get; set; }

        public decimal? PriceSuperCash { get; set; }

        public bool IsAssorted { get; set; }

        public string ErpCode { get; set; }

        public double SuperUnitConversionFactor { get; set; }

        public decimal PTD { get; set; }

        public long? ProductGSTCategoryId { get; set; }

        public decimal? StandardUnitWeight { get; set; }

        public decimal? UnitWeight { get; set; }

        public double ProductWeightinGm { get; set; }

        public int? HSNCode { get; set; }

        public string Category1 { get; set; }

        [Column("Product_AttributeText1")]
        public string AttributeText1 { get; set; }

        [Column("Product_AttributeText2")]
        public string AttributeText2 { get; set; }

        [Column("Product_AttributeBoolean1")]
        public bool? AttributeBoolean1 { get; set; }

        [Column("Product_AttributeBoolean2")]
        public bool? AttributeBoolean2 { get; set; }

        [Column("Product_AttributeNumber1")]
        public double? AttributeNumber1 { get; set; }

        [Column("Product_AttributeNumber2")]
        public double? AttributeNumber2 { get; set; }

        [Column("Product_AttributeDate1")]
        public DateTime? AttributeDate1 { get; set; }

        [Column("Product_AttributeDate2")]
        public DateTime? AttributeDate2 { get; set; }

        public int? MBQ { get; set; }

        public decimal? PTDSuper { get; set; }

        public decimal? PTDSub { get; set; }

        [Column("PTRMT")]
        public decimal? PTR_MT { get; set; }

        public bool IsSaleable { get; set; }

        public long? ProductCESSCategoryId { get; set; }

        public double? AdditionalUnitConversionFactor { get; set; }

        public string AdditionalUnit { get; set; }

        // public ProductSecondaryCategory ProductCategory { get; set; }
        // public ProductDisplayCategory ProductDisplayCategories { get; set; }
        public string ColorName { get; set; }

        public int? ExpiryInDays { get; set; }

        public string ProductThumbnail { get; set; }

        public string Category2 { get; set; }

        public string PackagingType { get; set; }

        [Column("MOQ")]
        public int? MOQ { get; set; }

        [Column("CategoryImage")]
        public string CategoryImage { get; set; }

        [ForeignKey("ProductCategoryId")]
        public ProductSecondaryCategory ProductCategory { get; set; }

        [ForeignKey("ProductDisplayCategoryId")]
        public ProductDisplayCategory ProductDisplayCategory { get; set; }

        [Column("NetWeight")]
        public decimal? NetWeight { get; set; }

        [Column("GrossWeight")]
        public decimal? GrossWeight { get; set; }

        [Column("PTRT")]
        public decimal? PTRT { get; set; }

        [Column("PTRDelta")]
        public double? PTRDelta { get; set; }

        [Column("VisibilityTag")]
        public ProductVisibilityTag ProductVisibilityTag { get; set; }

        [Column("ProductGroupID")]
        public long? ProductGroupId { get; set; }
    }
}
