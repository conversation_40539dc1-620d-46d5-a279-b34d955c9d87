﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace Library.DateTimeHelpers;

public static class DateTimeExtentions
{
    public static DateTime FromDateKey(int datekey)
    {
        var year = datekey / 10000;
        var month = datekey % 10000 / 100;
        var day = datekey % 100;
        return new DateTime(year, month, day);
    }

    public static DateTime FromUnixTime(this long unixTime)
    {
        var time = DateTimeOffset.FromUnixTimeSeconds(unixTime);
        return time.DateTime;
    }

    public static DateTime FromUnixTimeMilliseconds(this long unixTime)
    {
        var time = DateTimeOffset.FromUnixTimeMilliseconds(unixTime);
        return time.DateTime;
    }

    public static FA_MTD_LMTD Get_MTD_LMTD(this DateTime date, IEnumerable<FADateRange> journeyCycles,
        int monthStartDay, int yearStartMonth)
    {
        return new FA_MTD_LMTD
        {
            LMTD = GetLMTDDateRange(date, journeyCycles, monthStartDay, yearStartMonth),
            MTD = GetMTDDateRange(date, journeyCycles, monthStartDay, yearStartMonth),
            YTD = GetYTDDateRange(date, journeyCycles, monthStartDay, yearStartMonth)
        };
    }

    public static DateTime GetDateFromDateKey(this long dateKey)
    {
        return DateTime.ParseExact(dateKey.ToString(), "yyyyMMdd", CultureInfo.InvariantCulture,
            DateTimeStyles.None);
    }

    public static long GetDateKey(this DateTime dateTime)
    {
        return (dateTime.Year * 10000) + (dateTime.Month * 100) + dateTime.Day;
    }

    public static string GetDateKey_String(this DateTime dateTime)
    {
        return dateTime.ToString("yyyyMMdd");
    }

    public static int GetDayKey(this DateTime dayStartTime, int monthStartDay)
    {
        _ = dayStartTime.Day;
        int dayStartDay;
        if (monthStartDay > dayStartTime.Day)
        {
            var preCalMonth = dayStartTime.Month == 1
                ? new DateTime(dayStartTime.Year - 1, 12, 1)
                : new DateTime(dayStartTime.Year, dayStartTime.Month - 1, 1);
            var preMonthDays = DateTime.DaysInMonth(preCalMonth.Year, preCalMonth.Month);
            dayStartDay = preMonthDays - monthStartDay + dayStartTime.Day + 1;
        }
        else
        {
            dayStartDay = dayStartTime.Day - monthStartDay + 1;
        }

        return dayStartDay;
    }

    public static DateRangeMTDs GetLMTDDateRange(this DateTime dayStartTime, IEnumerable<FADateRange> journeyCycles,
        int monthStartDay, int yearStartMonth)
    {
        FADateRange prevMonth = null;
        if (journeyCycles != null && journeyCycles.Any())
        {
            foreach (var item in journeyCycles.OrderBy(i => i.StartDate))
            {
                if (dayStartTime.Date >= item.StartDate && dayStartTime.Date <= item.EndDate)
                {
                    if (prevMonth != null)
                    {
                        return new DateRangeMTDs
                        {
                            StartDate = prevMonth.StartDate,
                            EndDate = prevMonth.EndDate,
                            Today = prevMonth.StartDate.AddDays((dayStartTime.Date - item.StartDate).Days),
                            MonthNumber = prevMonth.MonthNumber,
                            YearNumber = prevMonth.YearNumber,
                            MonthName = prevMonth.MonthName,
                            CycleWeeks = prevMonth.CycleWeeks,
                            Week = prevMonth.CycleWeeks.FirstOrDefault(d =>
                                d.WeekStartDate <=
                                prevMonth.StartDate.AddDays((dayStartTime.Date - item.StartDate).Days) &&
                                d.WeekEndDate >=
                                prevMonth.StartDate.AddDays((dayStartTime.Date - item.StartDate).Days))
                        };
                    }

                    return new DateRangeMTDs
                    {
                        StartDate = item.StartDate,
                        EndDate = item.EndDate,
                        Today = dayStartTime.Date,
                        MonthNumber = item.EndDate.Month,
                        YearNumber = item.StartDate.Year,
                        MonthName = item.MonthName,
                        CycleWeeks = item.CycleWeeks,
                        Week = item.CycleWeeks.FirstOrDefault(d =>
                            d.WeekStartDate <= dayStartTime.Date && d.WeekEndDate >= dayStartTime.Date)
                    }.DefaultPrevMonth();
                }

                prevMonth = item;
            }
        }

        return GetLMTDDateRange(dayStartTime, monthStartDay, yearStartMonth);
    }

    public static DateRangeMTDs GetLMTDDateRange(this DateTime dateTime, int monthStartDay, int yearStartMonth)
    {
        return GetMTDDateRange(dateTime, monthStartDay, yearStartMonth).TempDefaultPrevMonth();
    }

    public static int GetMonthFromDateKey(long dateKey)
    {
        return Convert.ToInt32(dateKey.ToString().Substring(4, 2).TrimStart('0'));
    }

    public static int GetMonthKey(this DateTime dayStartTime, int monthStartDay, int yearStartMonth)
    {
        var month = dayStartTime.Month - yearStartMonth + 1;
        month = dayStartTime.Day < monthStartDay ? month - 1 : month;
        return month < 1 ? month + 12 : month;
    }

    public static DateRangeMTDs GetMTDDateRange(this DateTime dayStartTime, IEnumerable<FADateRange> journeyCycles,
        int monthStartDay, int yearStartMonth)
    {
        if (journeyCycles != null && journeyCycles.Any())
        {
            foreach (var item in journeyCycles.OrderBy(c => c.StartDate))
            {
                if (dayStartTime.Date >= item.StartDate && dayStartTime.Date <= item.EndDate)
                {
                    return new DateRangeMTDs
                    {
                        StartDate = item.StartDate,
                        EndDate = item.EndDate,
                        Today = dayStartTime.Date,
                        MonthNumber = item.MonthNumber,
                        YearNumber = item.YearNumber,
                        MonthName = item.MonthName,
                        CycleWeeks = item.CycleWeeks,
                        Week = item.CycleWeeks.FirstOrDefault(d =>
                            d.WeekStartDate <= dayStartTime.Date && d.WeekEndDate >= dayStartTime.Date)
                    };
                }
            }
        }

        return GetMTDDateRange(dayStartTime, monthStartDay, yearStartMonth);
    }

    public static DateRangeMTDs GetMTDDateRange(this DateTime dateTime, int monthStartDay, int yearStartMonth)
    {
        var monthNumber = dateTime.Month - yearStartMonth + 1;
        var yearNumber = dateTime.Year;
        if (monthNumber <= 0)
        {
            yearNumber--;
            monthNumber += 12;
        }

        if (monthStartDay > 1 && dateTime.Day >= monthStartDay)
        {
            monthNumber++;
            if (monthNumber > 12)
            {
                monthNumber -= 12;
                yearNumber++;
            }
        }

        var month = dateTime.Month;
        var year = dateTime.Year;
        if (monthStartDay > 1 && dateTime.Day < monthStartDay)
        {
            month--;
            if (month <= 0)
            {
                month += 12;
                year--;
            }
        }

        return DateRangeMTDs.GetDefaultRange(dateTime, new DateTime(year, month, monthStartDay), monthNumber,
            yearNumber);
    }

    public static DateTime GetMTDFromDate(this DateTime currentDate, int monthStartDay)
    {
        var date = currentDate;
        if (currentDate.Day < monthStartDay)
        {
            date = currentDate.AddMonths(-1);
        }

        return new DateTime(date.Year, date.Month, monthStartDay <= DateTime.DaysInMonth(date.Year, date.Month)
            ? monthStartDay
            : DateTime.DaysInMonth(date.Year, date.Month));
    }

    public static int GetQuarterFromMonth(long dateKey)
    {
        var month = GetMonthFromDateKey(dateKey);
        if (month <= 3)
        {
            return 1;
        }

        if (month is > 3 and <= 6)
        {
            return 2;
        }

        if (month is > 6 and <= 9)
        {
            return 3;
        }

        return 4;
    }

    public static string GetTimeString(this TimeSpan time)
    {
        return new DateTime().Add(time).ToString("HH:mm:ss");
    }

    public static int GetYearFromDateKey(long dateKey)
    {
        return Convert.ToInt32(dateKey.ToString().Substring(0, 4));
    }

    public static int GetYearKey(this DateTime dayStartTime, int monthStartDay, int yearStartMonth)
    {
        return dayStartTime.Month < yearStartMonth ||
               (dayStartTime.Month == yearStartMonth && dayStartTime.Day < monthStartDay)
            ? dayStartTime.Year - 1
            : dayStartTime.Year;
    }

    public static DateRangeMTDs GetYTDDateRange(this DateTime dayStartTime, IEnumerable<FADateRange> journeyCycles,
        int monthStartDay, int yearStartMonth)
    {
        return GetYTDDateRange(dayStartTime, monthStartDay, yearStartMonth);
    }

    public static DateRangeMTDs GetYTDDateRange(this DateTime dateTime, int monthStartDay, int yearStartMonth)
    {
        var month = dateTime.Month - yearStartMonth + 1;
        var year = dateTime.Year;
        if (monthStartDay > 1 && dateTime.Day < monthStartDay)
        {
            month--;
            if (month <= 0)
            {
                year--;
            }
        }

        return new DateRangeMTDs
        {
            StartDate = new DateTime(year, yearStartMonth, monthStartDay),
            EndDate = new DateTime(year, yearStartMonth, monthStartDay).AddYears(1).AddDays(-1),
            MonthNumber = 1,
            YearNumber = year,
            Today = dateTime.Date
        };
    }

    public static string MinutesToDatTimeStringConvertor(this int minutes)
    {
        if (minutes < 0)
        {
            return (minutes / 60).ToString("-00") + ":" + (Math.Abs(minutes) % 60).ToString("00");
        }

        return (minutes / 60).ToString("00") + ":" + (Math.Abs(minutes) % 60).ToString("00");
    }

    public static DateTimeOffset ToDateTimeOffset(this DateTime dateTime, TimeSpan offset)
    {
        return new DateTimeOffset(dateTime, offset);
    }

    public static long ToUnixTime(this DateTime date)
    {
        var offset = new DateTimeOffset(date);
        var epoch = offset.ToUnixTimeSeconds();
        return epoch;
    }

    public static long ToUnixTimeMilliseconds(this DateTime date)
    {
        var offset = new DateTimeOffset(date);
        var epoch = offset.ToUnixTimeMilliseconds();
        return epoch;
    }

    public class DateRangMin
    {
        public DateTime EndDate { get; set; }
        public int Month { get; set; }
        public DateTime StartDate { get; set; }
        public int Year { get; set; }
    }

    public static DateTime ToLocalTime(this DateTime dateTime, TimeSpan offSet)
    {
        return dateTime.Add(offSet);
    }
}

public class CycleWeek
{
    public int QuarterNumber { get; set; }
    public DateTime WeekEndDate { get; set; }
    public int WeekForMonth { get; set; }
    public int WeekForQuarter { get; set; }
    public int WeekForYear { get; set; }
    public DateTime WeekStartDate { get; set; }

    public static CycleWeek Create(DateTime startDate, DateTime endDate, int weekForMonth, int weekForQuarter, int weekForYear, int quarterNumber)
    {
        return new CycleWeek
        {
            WeekStartDate = startDate,
            WeekEndDate = endDate,
            WeekForMonth = weekForMonth,
            WeekForQuarter = weekForQuarter,
            WeekForYear = weekForYear,
            QuarterNumber = quarterNumber
        };
    }
}

public class DateRangeMTDs : FADateRange
{
    public DateTime Today { get; set; }

    public long TodayKey => Today.GetDateKey();

    public static DateRangeMTDs GetDefaultRange(DateTime today, DateTime? startDate = null, int? monthNumber = null,
        int? yearNumber = null)
    {
        startDate ??= new DateTime(today.Year, today.Month, 1);
        return new DateRangeMTDs
        {
            StartDate = startDate.Value,
            EndDate = startDate.Value.AddMonths(1).AddDays(-1),
            Today = today.Date,
            MonthNumber = monthNumber ??
                          (startDate.Value.Day > 1 ? startDate.Value.AddMonths(1).Month : startDate.Value.Month),
            YearNumber = yearNumber ?? startDate.Value.Year,
            MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(startDate.Value.Day > 1
                ? startDate.Value.AddMonths(1).Month
                : today.Month),
            CycleWeeks = GetWeeksForCalendarMonth(startDate.Value, startDate.Value.AddMonths(1).AddDays(-1)),
            Week = GetWeeksForCalendarMonth(startDate.Value, startDate.Value.AddMonths(1).AddDays(-1))
                .FirstOrDefault(d => d.WeekStartDate <= today.Date && d.WeekEndDate >= today.Date)
        };
    }

    public static List<CycleWeek> GetWeeksForCalendarMonth(DateTime startDate, DateTime endDate)
    {
        var weeks = new List<CycleWeek>();
        var diff = (endDate - startDate).TotalDays;
        var monthNumber = startDate.Day > 1 ? startDate.AddMonths(1).Month : startDate.Month;
        monthNumber = monthNumber < 4 ? monthNumber + 9 : monthNumber - 3;
        var quarterNumber = (monthNumber - 1) / 3 + 1 <= 4 ? (monthNumber - 1) / 3 + 1 : 4;
        var startYear = monthNumber <= 9 ? startDate.Year : startDate.Year - 1;
        var yearStartDate = new DateTime(startYear, 4, 1);
        var quarterStartDate = yearStartDate.AddMonths((quarterNumber - 1) * 3);
        var yearWeekNumber = (startDate - yearStartDate).TotalDays / 7 + 1;
        var quarterWeekNumber = (startDate - quarterStartDate).TotalDays / 7 + 1;
        var weekOfMonth = 1;

        while (diff >= 0)
        {
            var week = new CycleWeek();
            var dayOfWeek = weekOfMonth == 1 ? (int)startDate.DayOfWeek : 1;
            dayOfWeek = dayOfWeek == 0 ? 7 : dayOfWeek; // Adjust for Sunday

            week.WeekStartDate = startDate;
            week.WeekEndDate = (diff >= 6)
                ? week.WeekStartDate.AddDays(6 - (dayOfWeek - 1))
                : week.WeekStartDate.AddDays(diff);

            week.WeekForMonth = weekOfMonth;
            week.WeekForQuarter = (int)quarterWeekNumber;
            week.WeekForYear = (int)yearWeekNumber;
            week.QuarterNumber = quarterNumber;

            weeks.Add(week);

            weekOfMonth++;
            quarterWeekNumber++;
            yearWeekNumber++;

            startDate = week.WeekEndDate.AddDays(1);
            diff = (endDate - startDate).TotalDays; // Recalculate diff
        }

        // Handle the case where the last day of the month is a Monday
        if (endDate.DayOfWeek == DayOfWeek.Monday && !weeks.Any(w => w.WeekEndDate == endDate))
        {
            weeks.Add(CycleWeek.Create(endDate, endDate, weekOfMonth, (int)quarterWeekNumber, (int)yearWeekNumber, quarterNumber));
        }

        return weeks;
    }

    public DateRangeMTDs DefaultPrevMonth()
    {
        var prevMonthRange = new DateRangeMTDs
        {
            EndDate = StartDate.AddDays(-1),
            StartDate = StartDate.AddMonths(-1),
            Today = StartDate.AddDays(-1).Month == 2 && (Today.Date - StartDate.Date).Days >=
                DateTime.DaysInMonth(StartDate.AddDays(-1).Year, 2)
                    ? StartDate.AddMonths(-1)
                        .AddDays(DateTime.DaysInMonth(StartDate.AddDays(-1).Year, 2)).AddDays(-1)
                    : (Today.Date - StartDate.Date).Days >= 30
                        ? StartDate.AddMonths(-1).AddDays((Today.Date - StartDate.Date).Days).AddDays(-1)
                        : StartDate.AddMonths(-1).AddDays((Today.Date - StartDate.Date).Days),
            MonthNumber = MonthNumber - 1 == 0 ? 12 : MonthNumber - 1,
            //Adjustments
            YearNumber = MonthNumber - 1 == 0 ? YearNumber - 1 : YearNumber
        };
        prevMonthRange.MonthName =
            CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(prevMonthRange.StartDate.Day > 1
                ? StartDate.Month
                : prevMonthRange.StartDate.Month);
        prevMonthRange.CycleWeeks = GetWeeksForCalendarMonth(prevMonthRange.StartDate, prevMonthRange.EndDate);
        return prevMonthRange;
    }
    public DateRangeMTDs TempDefaultPrevMonth()
    {
        var prevMonthRange = new DateRangeMTDs
        {
            EndDate = StartDate.AddDays(-1),
            StartDate = StartDate.AddMonths(-1),
            Today = StartDate.AddDays(-1).Month == 2 && (Today.Date - StartDate.Date).Days >=
                DateTime.DaysInMonth(StartDate.AddDays(-1).Year, 2)
                    ? StartDate.AddMonths(-1)
                        .AddDays(DateTime.DaysInMonth(StartDate.AddDays(-1).Year, 2)).AddDays(-1)
                    : (Today.Date - StartDate.Date).Days > 30
                        ? StartDate.AddMonths(-1).AddDays((Today.Date - StartDate.Date).Days).AddDays(-1)
                        : StartDate.AddMonths(-1).AddDays((Today.Date - StartDate.Date).Days),
            MonthNumber = MonthNumber - 1 == 0 ? 12 : MonthNumber - 1,
            //Adjustments
            YearNumber = MonthNumber - 1 == 0 ? YearNumber - 1 : YearNumber
        };
        prevMonthRange.MonthName =
            CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(prevMonthRange.StartDate.Day > 1
                ? StartDate.Month
                : prevMonthRange.StartDate.Month);
        prevMonthRange.CycleWeeks = GetWeeksForCalendarMonth(prevMonthRange.StartDate, prevMonthRange.EndDate);
        return prevMonthRange;
    }
}

public class FA_MTD_LMTD
{
    public DateRangeMTDs LMTD { get; set; }
    public DateRangeMTDs MTD { get; set; }
    public DateRangeMTDs YTD { get; set; }
}

public class FADateRange
{
    public ICollection<CycleWeek> CycleWeeks { get; set; }
    public DateTime EndDate { get; set; }
    public long EndDateKey => EndDate.GetDateKey();
    public string MonthName { get; set; }
    public int MonthNumber { get; set; }
    public DateTime StartDate { get; set; }
    public long StartDateKey => StartDate.GetDateKey();
    public CycleWeek Week { get; set; }
    public int YearNumber { get; set; }
}
