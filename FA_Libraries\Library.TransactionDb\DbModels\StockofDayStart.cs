﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace Library.TransactionDb.DbModels;

public class StockofDayStart
{
    public long Id { get; set; }

    public VanDayStock VanDayStartStock { get; set; }

    public long PrimaryCategoryId { get; set; }

    public long SecondaryCategoryId { get; set; }

    public long ProductDivisionId { get; set; }

    public double StandardUnitConversionFactor { get; set; }

    public long ProductId { get; set; }

    public long VansStock { get; set; }

    public double Weight { get; set; }

    public double PTR { get; set; }

    public Guid Guid { get; set; }

    public long? FreshStock { get; set; }

    public long? CarryForwardStock { get; set; }

    public bool? IsEdited { get; set; }

    public long? Delta { get; set; }

    public string BatchNo { get; set; }

    public DateTime? MfgDate { get; set; }

    public double? InventoryPrice { get; set; }

    public long? OldStock { get; set; }

    [Column("VanDayStartStockId")]
    public long VanDayStartStockId { get; set; }

    public long? FreeQty { get; set; }
}
