using Microsoft.Extensions.DependencyInjection;
using HCCB.Tests.Configurations;
using Libraries.CommonEnums;
using HCCBCtsApprovalProcessor;
using HCCBCtsApprovalProcessor.Configuration;
using Core.Models;

namespace HCCB.Tests.CtsApprovalProcessor
{
    [TestClass]
    public class CtsApprovalTests
    {
        private ServiceProvider serviceProvider;

        [TestInitialize]
        public void Initialise()
        {
            Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageReadOnly.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, configuration);
            serviceCollection.AddScoped<CtsApproval>();
            serviceProvider = serviceCollection.BuildServiceProvider();
        }


        [TestMethod]
        public void CtsApprovalTimeLine()
        {
            var qp = serviceProvider.GetRequiredService<CtsApproval>();
            var data = new QueueData()
            {
                CompanyId = 193017,
                RequestId = 170979,
                ApprovalEngineRequestType = ApprovalEngineRequestType.AssetAllocation,
                AssetReallocationType = AssetReallocationType.Breakdown
            };
            qp.CtsApprovalTimeLine(data).Wait();
        }
    }
}