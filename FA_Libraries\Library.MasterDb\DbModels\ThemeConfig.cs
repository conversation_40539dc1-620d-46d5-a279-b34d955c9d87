﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("AppConfig")]
    public class ThemeConfig
    {
        public long Id { get; set; }

        public long? CompanyId { get; set; }

        public int? UIType { get; set; }

        public string CompanyLogo { get; set; }

        public string ColourCode { get; set; }

        public string AppModules { get; set; }

        public string Constraints { get; set; }

        public string ProductFilters { get; set; }

        public string UIConfigurations { get; set; }

        [Column("CTAsColor")]
        public string CTAsColor { get; set; }

        [Column("SecondaryCompanyLogo")]
        public string SecondaryCompanyLogo { get; set; }

        [Column("PreferredLanguages")]
        public string PreferredLanguages { get; set; }

        [Column("CTAicons")]
        public string CTAicons { get; set; }
        [Column("ExtendedUIConfig")]
        public string ExtendedUIConfig { get; set; }

        [Column("OutletListUIType")]
        public int OutletListUIType { get; set; }

        [Column("ConfigurableKPIs")]
        public string ConfigurableKPIs { get; set; }
    }
}
