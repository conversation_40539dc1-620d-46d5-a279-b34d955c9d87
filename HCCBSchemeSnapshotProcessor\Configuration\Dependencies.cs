using Azure.Storage.Queues;
using Core.Constants;
using Core.Loggers;
using HCCB.DbStorage.DbContexts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SlackNet;
using SlackNet.Extensions.DependencyInjection;

namespace HCCBSchemeSnapshotProcessor.Configuration
{
    public static class Dependencies
    {
        public static string ChannelId = "C08D4PSBW4V";

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            ChannelId = config.GetValue<string>("Slack:ChannelId") ?? ChannelId;
            
            var azureWebJobsStorage = config.GetWebJobsConnectionString("AzureWebJobsStorage");
            var writableMasterDbConnectionString = config.GetConnectionString("WritableMasterDbConnectionString");

            // Register MasterDbSqlDataReader with 10-minute timeout (600 seconds)
            services.AddScoped(e =>
                new MasterDbSqlDataReader(writableMasterDbConnectionString, 600));

            // Register QueueClient for manual queue operations
            services.AddKeyedSingleton(HCCBStorageQueues.SchemeSnapshotUpdateQueue, (sp,_) =>
            {
                var queueServiceClient = new QueueServiceClient(azureWebJobsStorage);
                return queueServiceClient.GetQueueClient(HCCBStorageQueues.SchemeSnapshotUpdateQueue);
            });

            // Slack Configuration
            services.AddHttpClient();
            services.AddSlackNet(c => c
                .UseApiToken(config.GetValue<string>("Slack:ApiToken")));
            services.AddSingleton<ISlackLogHelper>(sp => 
                new SlackLogHelperV2(
                    sp.GetRequiredService<IHttpClientFactory>(),
                    sp.GetRequiredService<ISlackApiClient>(), 
                    masterStorageConnectionString: string.Empty, 
                    ChannelId));
        }
    }
}
