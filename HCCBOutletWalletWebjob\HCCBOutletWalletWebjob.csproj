﻿<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">
	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.4" />
	</ItemGroup>
	<ItemGroup>
	  <PackageReference Include="Telegram.Bot" Version="19.0.0" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\Core\Core.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Library.ResponseHelpers\Library.ResponseHelpers.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="appsettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="localhost.cer">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>