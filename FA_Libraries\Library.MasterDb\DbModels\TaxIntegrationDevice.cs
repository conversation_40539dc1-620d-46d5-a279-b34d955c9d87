﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("TaxIntegrationDevices")]
    public class TaxIntegrationDevice
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(64)]
        public string DeviceName { get; set; }

        public TaxIntegrationType IntegrationType { get; set; }

        [Required]
        [StringLength(64)]
        public string CountryName { get; set; }

        [Required]
        [Column(TypeName = "nvarchar(max)")]
        public string ConfigJson { get; set; }

        public bool IsDeleted { get; set; }
    }

    public enum TaxIntegrationType
    {
        TIMS,
        ETIMS
    }
}
