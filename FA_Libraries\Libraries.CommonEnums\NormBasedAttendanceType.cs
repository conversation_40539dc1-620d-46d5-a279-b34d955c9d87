﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonEnums;

public enum AchevementStatus
{
    NotAchieved = 0,
    HalfDay = 1,
    Fullday = 2
}

public enum AttendanceRegulariseStatus
{
    Pending = 0,
    Approved = 1,
    Rejected = 2
}

public enum AttendanceRegulariseStatusFrom
{
    AnalyticsApp,
    Dashboard
}

public enum KRAAttendanceNorm
{
    DayStartTime,
    RetailingTime,
    TC,
    TotalTime,
    JourneyPlanAdherence,
    JointWorkingTime,
    Callsagainstplan,
    PC,
    FirstCallTime,
    NetSalesPerDay,
    LinesPerCall,
    AverageLPC,
    PhysicalTC,
    PercentageEffectivePC,
    EffectivePC,
    CallTime,
    OVC,
    TelephonicOrders
}

public enum NormBasedAttendanceType
{
    Present,
    HalfDay,
    CalculatedAbsent,
    Absent,
    Leave,
    WeeklyOff,
    Holiday
}
