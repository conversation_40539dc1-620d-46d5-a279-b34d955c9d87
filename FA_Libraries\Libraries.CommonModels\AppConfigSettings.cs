﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonModels
{
    public class AppConfigSettings
    {
        public string StorageConnectionString { get; set; }

        public string reportApiBaseUrl { get; set; }
        public string dashboardApiBaseUrl { get; set; }
        public string dashboardBaseUrl { get; set; }
        public string reportApiToken { get; set; }

        public string deployments { get; set; }

        public string flexibleTargetAchievementApiToken { get; set; }

        public string portalBaseURL { get; set; }

        public string otpReadingHashCode { get; set; }

        public string floTadaApiBaseUrl { get; set; }

        public string mtApiBaseUrl { get; set; }

        public string newPortalBaseUrl { get; set; }

        public string DmsShorteningUrl { get; set; }

        public string ShortenedBaseUrl { get; set; }

        public string idsSalt { get; set; }

        public string projectId { get; set; }

        public string MasterStorageConnectionString { get; set; }

        public string HCCBStorageConnectionString { get; set; }

        public string NsAppApiBaseUrl { get; set; }

        public int MaxUserAllowedSession { get; set; }

        public int MaxAllowedSessionTime { get; set; }

        public string CosmosApiLogsContainerName { get; set; }

        public string IdsBaseUrl { get; set; }

        public string FaiDataLakeStorageConnectionString { get; set; }

        public string ImageBaseUrl { get; set; }

        public string VueDashboardBaseUrl { get; set; }

        public string OpenMarketBaseUrl { get; set; }

        public bool IsProduction { get; set; }

        public string WorkcozyBaseUrl { get; set; }

        public string WorkcozyAuthToken { get; set; }
        public string tspApiBaseUrl { get; set; }
        public string tspApiToken { get; set; }
        public string FaisBaseUrl { get; set; }
    }
}
