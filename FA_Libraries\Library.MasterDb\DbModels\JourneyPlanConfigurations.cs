﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels;

public class JourneyPlanConfigurations
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public JourneyCreationSettingType? JourneyCreationSettingType { get; set; }

    public PositionCodeLevel? PositionLevel { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public string EntityLevel { get; set; }
}
