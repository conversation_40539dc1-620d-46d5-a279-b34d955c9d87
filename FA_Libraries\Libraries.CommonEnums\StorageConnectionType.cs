﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

/// <summary>
/// Enum to specify which Azure Blob Storage connection string to use for file operations
/// </summary>
public enum StorageConnectionType
{
    /// <summary>
    /// Default storage connection - StorageConnectionString
    /// </summary>
    [Display(Name = "storage")]
    Storage,

    /// <summary>
    /// Master storage connection - MasterStorageConnectionString
    /// </summary>
    [Display(Name = "master")]
    Master,

    /// <summary>
    /// FAI Data Lake storage connection - FaiDataLakeConnectionString
    /// </summary>
    [Display(Name = "faiadls")]
    FaiDataLake,

    /// <summary>
    /// Image detection storage connection - ImageStorageConnectionString
    /// </summary>
    [Display(Name = "imagedetect")]
    ImageDetect
}
