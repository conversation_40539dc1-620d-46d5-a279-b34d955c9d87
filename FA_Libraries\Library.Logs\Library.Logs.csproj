﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Confluent.Kafka" Version="2.11.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Azure.DocumentDB.Core" Version="2.22.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
    <PackageReference Include="Polly" Version="8.6.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
    <ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>

</Project>
