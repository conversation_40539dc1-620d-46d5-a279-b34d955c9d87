﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    public class CompanyTargets
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long Hierarchy1Id { get; set; }

        public long? Hierarchy2Id { get; set; }

        public long? Hierarchy3Id { get; set; }

        [ForeignKey("CompanyTargetSubscriptions")]
        public long CompanyTargetSubscriptionId { get; set; }

        public FlexibleTargetFrequency Frequency { get; set; }

        public bool IsVisitBasis { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public double Target { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public virtual CompanyTargetSubscriptions CompanyTargetSubscriptions { get; set; }
        public bool IsDeactive { get; set; }
    }
}
