﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using Library.Infrastructure.Interface;

namespace Library.SlackService.Model;

public interface ISlackMessage : IQueueMessage
{
    List<Attachment> Attachments { get; set; }
    string Text { get; set; }
    string Username { get; set; }
}

public class Attachment
{
    public string Color { get; set; }
    public string FallBack { get; set; }
    public List<Field> Fields { get; set; }
    public string Title { get; set; }
    public string Text { get; set; }
}

public class Field
{
    public bool Short { get; set; }
    public string Title { get; set; }
    public string Value { get; set; }
}

public class SlackMessage : ISlackMessage
{
    public string Channel { get; set; }
    public List<Attachment> Attachments { get; set; }
    public string Text { get; set; }
    public string Username { get; set; }
}
