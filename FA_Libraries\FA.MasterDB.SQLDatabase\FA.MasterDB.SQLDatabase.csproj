﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="EntityFramework" Version="6.5.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FA.MasterDB\FA.MasterDB.Core.csproj" />
    <ProjectReference Include="..\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\Library.SqlHelper\Library.SqlHelper.csproj" />
  </ItemGroup>

</Project>
