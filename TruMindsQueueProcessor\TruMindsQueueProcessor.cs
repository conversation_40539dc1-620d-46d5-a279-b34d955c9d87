﻿using Microsoft.Azure.WebJobs;
using Core.Loggers;
using Core.Repositories;
using TruMindsQueueProcessor.Services;
using Core.Models.QueueModel;
using Library.SlackService.Interfaces;
using Opw.HttpExceptions;

namespace TruMindsQueueProcessor
{
    public class TruMindsQueueProcessorJob
    {
        private readonly TruMindsSyncService _syncService;
        private readonly IMaxerienceLogRepository _repository;
        private readonly ISlackLogger _slackLogger;


        public TruMindsQueueProcessorJob(
            TruMindsSyncService syncService,
            IMaxerienceLogRepository repository,
            ISlackLogger slackLogger)
        {
            _syncService = syncService;
            _repository = repository;
            _slackLogger = slackLogger;
        }

        [FunctionName("TruMindsQueueProcessor")]
        public async Task Run([QueueTrigger("hccb-truminds-queue")] TruMindsSyncQueueData queueData)
        {
            Console.WriteLine($"Processing session: {queueData.MaxerienceSessionId}");

            try
            {
                var log = await _repository.GetLogAsync(queueData.MaxerienceLogId);

                if (log == null)
                {
                    Console.WriteLine($"Log not found: {queueData.MaxerienceLogId}");
                    return;
                }

                var scenes = await _repository.GetScenesForLogAsync(queueData.MaxerienceLogId, queueData.CompanyId);
                var success =  await _syncService.SendToTruMindsFormDataAsync(log, scenes);

                if(success) await _repository.UpdateSyncStatusAsync(log.Id, true);
                Console.WriteLine($"Successfully synced: {log.MaxerienceSessionId}");
            }
            catch (Exception ex)
            {
                var logMessage = "TruMinds Sync Error: Unhandled exception";
                if (ex is HttpException httpException)
                {
                    var timeStamp = TruMindsSyncService.GetCurrentISTTimestamp();

                    logMessage = $"❌ Failure: TruMinds API - <Timestamp [IST]: {timeStamp}>\n" +
                                $"Failure HTTP Status: {httpException.StatusCode}\n";
                }
                Console.WriteLine($"Error processing session {queueData.MaxerienceSessionId}: {ex}");

                var queueDataJson = System.Text.Json.JsonSerializer.Serialize(queueData);

                Console.WriteLine($"Queue Data (for debugging): {queueDataJson}");

                _slackLogger.LogError(logMessage: logMessage,
                    ex: ex,
                    metaData: queueData
                );
            }
        }
    }
}