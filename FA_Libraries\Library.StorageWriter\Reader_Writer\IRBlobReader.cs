﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Library.StorageWriter.Reader_Writer;

public class IRPosmBlobReader : BlobReader
{
    public IRPosmBlobReader(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }
}

public class IREoeBlobReader : BlobReader
{
    public IREoeBlobReader(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }
}

public class IRPlanogramBlobReader : BlobReader
{
    public IRPlanogramBlobReader(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }
}

public class IROcrBlobReader : BlobReader
{
    public IROcrBlobReader(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }
}

public class IRPlanogramBlobWriter : BlobWriter
{
    public IRPlanogramBlobWriter(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }
}

public class IRPosmBlobWriter : BlobWriter
{
    public IRPosmBlobWriter(string storageConnectionString, string containerName) : base(storageConnectionString, containerName)
    {
    }
}
