# HCCB Scheme Snapshot Processor

## Overview
This Azure WebJob processes scheme snapshot updates by reading from the `scheme-snapshot-update-queue` and refreshing the HCCBSchemeSnapshot table with current scheme data.

## Features
- **Queue Triggered**: Processes messages from `scheme-snapshot-update-queue`
- **Single Item Processing**: Configured with BatchSize = 1 to process one item at a time
- **Batch Collection**: After receiving initial message, waits 10 minutes then reads all available messages
- **Database Update**: Executes SQL query to refresh HCCBSchemeSnapshot table
- **Error Handling**: Sends error notifications to Slack channel

## Process Flow
1. Receives initial message from `scheme-snapshot-update-queue`
2. Waits 10 minutes to allow additional messages to accumulate
3. Reads all available messages from the queue
4. Deletes all processed messages from the queue
5. Executes SQL query to refresh HCCBSchemeSnapshot table with current scheme data
6. Sends error notifications to Slack if any step fails

## SQL Query
The WebJob executes a query that:
- Calculates IST "today" using UTC + 330 minutes
- Truncates the existing HCCBSchemeSnapshot table
- Inserts fresh snapshot data from active schemes for companies 193054 and 193017
- Filters for active, non-deleted, non-QPS, non-two-qualifier schemes
- Uses a 10-minute command timeout

## Configuration
- **Queue**: `scheme-snapshot-update-queue` (defined in Core\Constants\HCCBStorageQueues.cs)
- **Database**: Uses WritableHccbDbConnectionString for database operations
- **Slack**: Configured for error notifications to channel C08D4PSBW4V
- **Timeout**: 10-minute timeout for SQL query execution

## Dependencies
- Azure.Storage.Queues for queue operations
- HCCB.DbStorage for database access
- Core for constants and logging
- SlackNet for error notifications

## Error Handling
All errors are logged and sent to the configured Slack channel with detailed error information including:
- Error message
- Stack trace
- Timestamp in IST

## Deployment
This WebJob is part of the HCCB solution under the ADLS folder and targets .NET 9.0.
