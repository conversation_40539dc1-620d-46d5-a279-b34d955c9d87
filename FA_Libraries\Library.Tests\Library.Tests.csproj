<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
<OutputType>Library</OutputType>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.10.2" />
    <PackageReference Include="MSTest.TestFramework" Version="3.10.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Library.ResiliencyHelpers\Library.ResiliencyHelpers.csproj" />
    <ProjectReference Include="..\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj" />
  </ItemGroup>

</Project>
