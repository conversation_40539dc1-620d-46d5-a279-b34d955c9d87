﻿// Copyright (c) FieldAssist. All Rights Reserved.
namespace Libraries.CommonEnums;

public enum RouteAutomationStatus
{
    Requested = 0,
    Executed = 1,
    ErrorDuringExecution = 2,
    Executing = 3,
    UploadStarted = 4,
    ErrorDuringUpload = 5,
    UploadCompleted = 6
}

public enum DJPStatus
{
    Requested = 0,
    Executed = 1,
    ErrorDuringExecution = 2,
    Executing = 3,
    UploadStarted = 4,
    ErrorDuringUpload = 5,
    UploadCompleted = 6
}

public enum TerritoryOptimizationStatus
{
    Requested = 0,
    Executing = 10,
    Executed = 20,
    ErrorDuringExecution = 30
}

public enum RoutePlaygroundStatus
{
    Requested = 0,
    Executing = 10,
    Executed = 20,
    ErrorDuringExecution = 30
}

public enum InputType
{
    Manual = 0,
    Automatic = 1
}

public enum VisitDefinitionType
{
    Segmentation = 0,
    Channel = 1,
    ShopType = 2
}

public enum RouteFrequency
{
    Weekly = 0,
    Fortnightly = 1,
    Monthly = 2,
}

public enum SpectralCoefficientType
{
    Low = 0,
    Medium = 1,
    High = 2
}
public enum OutlierAddition
{
    OutlierAdditionOff = 0,
    OutlierAdditionOnWithOneVisit = 1
}
