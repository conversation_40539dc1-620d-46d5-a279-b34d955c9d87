﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Library.SqlHelper
{
    public static class SqlParameterHelper
    {
        /// <summary>
        /// Extracts all unique parameter names from a SQL query
        /// </summary>
        /// <param name="query">The SQL query to extract parameters from</param>
        /// <returns>Collection of parameter names (including @ symbol)</returns>
        public static List<string> ExtractParameterNames(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return new List<string>();
            }

            const string pattern = @"@\b\S+?\b";
            var matches = Regex.Matches(query, pattern);
            var paramNames = matches
                .Select(m => m.Value)
                .Distinct()
                .ToList();

            return paramNames;
        }
    }
}
