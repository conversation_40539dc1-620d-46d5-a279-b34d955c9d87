﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    public class LocationWithBeatOrRoutesAndLastUpdatedToken
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Address { get; set; }

        public string ShopType { get; set; }

        public OutletSegmentation Segmentation { get; set; }

        public DateTime DOC { get; set; }

        public long? BeatId { get; set; }

        public List<long> RouteIds { get; set; }

        public DateTime UpdateToken { get; set; }

        public string OwnersName { get; set; }

        public string Image { get; set; }

        public string Guid { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        public double? GeoAccuracy { get; set; }

        public bool IsResetLocation { get; set; }

        public string GSTIN { get; set; }

        public string OwnersNo { get; set; }

        public long? ShopTypeId { get; set; }

        public bool IsFocused { get; set; }

        public bool IsKYC { get; set; }

        public string Market { get; set; }

        public string City { get; set; }

        public string PlaceOfDelivery { get; set; }

        public string ErpId { get; set; }

        public OutletChannel OutletChannel { get; set; }

        public VerificationStatus VerificationStatus { get; set; }

        public CFStatus? CFStatus { get; set; }

        public string PAN { get; set; }

        public List<AssetMappingDetail> AssetMappingDetails { get; set; }

        public long? GeographicalMappingId { get; set; }

        public string FSSAINumber { get; set; }

        public DateTime? FSSAIExpiryDate { get; set; }

        public List<long> CustomTagList { get; set; }

        public dynamic InfoJson { get; set; }

        public IsKYCEnum? IsKYCEnum { get; set; }

        public string State { get; set; }

        public string PinCode { get; set; }

        [Column("AttributeText3")]
        public string AttributeText3 { get; set; }
        public string AttributeText1 { get; set; }
        public string AttributeText2 { get; set; }
        public string AttributeText4 { get; set; }
        public string TIN { get; set; }

        public long? EntityMarginSlabId { get; set; }

        public DateTime CreatedAt { get; set; }

        public long? SubShopTypeId { get; set; }

        public string? SubShopType { get; set; }

        public string TownName { get; set; }
    }
}
