﻿// Copyright (c) FieldAssist. All Rights Reserved.

using SlackNet.WebApi;
using System.Threading.Tasks;
using System.IO;
using System;
using System.Text.Json;
using Library.SlackService.Interfaces;

namespace Library.SlackService.Services
{
    public class DirectSlackLogHelper(ISlackBotService slackBotService) : IDirectSlackLogHelper
    {
        private readonly ISlackBotService _slackBotService = slackBotService;

        public async Task<bool> SendFileToChannel(FileInfo logFile, string channelId, bool compressFile = false)
        {
            try
            {
                await _slackBotService.JoinChannel(channelId);
                await _slackBotService.SendFileToChannel(logFile, channelId, compressFile);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed To Send File to Slack.\nError:\n{ex}");
                return false;
            }
        }

        public async Task<bool> SendMessage(Message message)
        {
            try
            {
                await _slackBotService.SendMessage(message);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed To Send Message to Slack.\nError:\n{ex}");
                Console.WriteLine($"Message:\n{JsonSerializer.Serialize(message)}");
                return false;
            }
        }

        public async Task<bool> SendMessageToChannel(string message, string channel, string iconEmoji = ":fa:", string userName = "FA Slack Bot")
        {
            try
            {
                await _slackBotService.SendMessageToChannel(message, channel, iconEmoji, userName);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed To Send Message to Slack.\nError:\n{ex}");
                Console.WriteLine($"Message:\n{message}");
                return false;
            }
        }
    }
}
