﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <!--        <Nullable>enable</Nullable>-->
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="microsoft.entityframeworkcore.sqlserver" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Utilities" Version="17.14.40264" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\Library.Core.SfaTransaction\Library.Core.SfaTransaction.csproj" />
    <ProjectReference Include="..\EntityHelper\EntityHelper.csproj" />
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Library.CommonHelpers\Library.CommonHelpers.csproj" />
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
    <ProjectReference Include="..\Library.SMSHelpers\Library.SMSHelpers.csproj" />
  </ItemGroup>
</Project>
