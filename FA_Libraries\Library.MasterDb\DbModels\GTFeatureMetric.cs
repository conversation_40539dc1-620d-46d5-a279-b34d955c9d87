﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    [Table("GTFeatureMetrics")]
    public class GTFeatureMetric
    {
        [Key]
        public long Id { get; set; }
        public long GTFeatureId { get; set; }
        public GTMetricEnums MetricEnum { get; set; }
        public string Name { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        [ForeignKey("GTFeatureId")]
        public virtual GTFeature GTFeature { get; set; }
        public virtual ICollection<CompanyWiseGTFeatureMetric> CompanyWiseGTFeatureMetrics { get; set; }
    }
}
