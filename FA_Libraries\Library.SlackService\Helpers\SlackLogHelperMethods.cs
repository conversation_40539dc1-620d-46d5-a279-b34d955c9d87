﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using SlackNet.Blocks;
using System.Collections.Generic;
using Library.SlackService.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Library.SlackService.Interfaces;
using System.Linq;
using System.Text.Json;
using Library.SlackService.Model;
using Microsoft.Extensions.Logging;

namespace Library.SlackService.Helpers
{
    public static class SlackLogHelperMethods
    {
        internal const string SlackWebHookClientName = "SlackWebHookClient";
        private const int StrackTraceMaxLength = 1500;

        public static IList<TextObject> GetFieldsForException(Exception ex)
        {
            var stackTrace = ex.StackTrace != null && ex.StackTrace.Length > StrackTraceMaxLength
                        ? string.Concat("...\n", ex.StackTrace.AsSpan(ex.StackTrace.Length - 1 - StrackTraceMaxLength, StrackTraceMaxLength))
                        : ex.StackTrace;

            var fields = new List<TextObject>(3)
            {
                new Markdown($"*Message:*\n```{ex.Message}```"),
                new Markdown($"*Base Message:*\n```{ex.GetBaseException().Message}```"),
                new Markdown($"*StackTrace:*\n```{stackTrace}```")
            };

            return fields;
        }

        public static IList<TextObject> GetFieldsForMetaData(object metaData)
        {
            if (metaData == null)
            {
                return new List<TextObject>();
            }

            var props = metaData.GetType().GetProperties();
            var fields = new List<TextObject>(props.Count()); 
            foreach (var prop in props)
            {
                var value = prop.GetValue(metaData);
                if (value != null)
                {
                    fields.Add(new Markdown($"*{prop.Name}:*\n{value}"));
                }
            }

            return fields;
        }

        public static void AddSlackDirectLogger(this IServiceCollection services, IConfiguration configuration, string webHookConfigKey = default)
        {
            webHookConfigKey = webHookConfigKey ?? "Slack:WebHook";
            var SlackWebHook = new Uri(configuration.GetValue<string>(webHookConfigKey));
            services.AddHttpClient(SlackWebHookClientName, c => c.BaseAddress = SlackWebHook); 
            services.AddSingleton<ISlackLogger, SlackDirectLogger>();
            // Register the same singleton also as a hosted service
            services.AddHostedService(provider =>
                (SlackDirectLogger)provider.GetRequiredService<ISlackLogger>());
        }

        internal static string GetLogForException(Exception ex)
        {
            var stackTrace = ex.StackTrace != null && ex.StackTrace.Length > StrackTraceMaxLength
                        ? string.Concat("...\n", ex.StackTrace.AsSpan(ex.StackTrace.Length - 1 - StrackTraceMaxLength, StrackTraceMaxLength))
                        : ex.StackTrace;
            return $"Message: ```{ex.Message}```\n" +
                (ex.Message != ex.GetBaseException().Message ? $"Base Message: ```{ex.GetBaseException().Message}```\n" : string.Empty) +
                $"Stack Trace: ```{stackTrace}```";
        }

        internal static string GetLogForMetaData(object metaData)
        {
            return $"```{JsonSerializer.Serialize(metaData)}```";
        }

        internal static SlackMessage GetSlackMessage(string logMessage, Exception ex, object metaData, LogLevel logLevel)
        {
            var logColor = GetLogLevelColor(logLevel);
            var slackMessage = new SlackMessage
            {
                Attachments = new List<Attachment>
                {
                    new Attachment
                    {
                        Color = logColor,
                        Title = "LogMessage",
                        Text = logMessage
                    }
                }
            };

            if (metaData != null)
            {
                slackMessage.Attachments.Add(
                    new Attachment
                    {
                        Color = logColor,
                        Title = "MetaData",
                        Text = GetLogForMetaData(metaData)
                    });
            }

            if (ex != null)
            {
                slackMessage.Attachments.Add(
                    new Attachment
                    {
                        Color = logColor,
                        Title = "Exception Details",
                        Text = GetLogForException(ex)
                    });
            }

            return slackMessage;
        }

        private static string GetLogLevelColor(LogLevel logLevel)
        {
            switch (logLevel)
            {
                case LogLevel.Trace:
                case LogLevel.Debug:
                case LogLevel.Information:
                    return "blue";
                case LogLevel.Warning:
                    return "orange";
                case LogLevel.Error:
                case LogLevel.Critical:
                    return "danger";
                case LogLevel.None:
                default:
                    return "grey";
            }
        }
    }
}
