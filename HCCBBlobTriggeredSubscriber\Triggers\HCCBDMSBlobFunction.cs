using Core.Abstracts;
using Core.Loggers;
using Core.Models.HccbDbModels;
using Core.Models;
using Microsoft.Azure.WebJobs;
using SlackNet;
using Core.Repositories;
using HCCBBlobTriggeredSubscriber.Configurations;
using HCCBBlobTriggeredSubscriber.Services;
using HCCBBlobTriggeredSubscriber.Helpers;
using HCCBBlobTriggeredSubscriber.Models;
using Library.Infrastructure.QueueService;
using Core.Constants;

namespace HCCBBlobTriggeredSubscriber.Triggers
{
    public class HCCBDMSBlobFunction : ProcessorBaseIntegrationLogs
    {
        private const string LogDirectory = "Logs";
        private readonly IDistributorStockService distributorStockService;
        private readonly IProductPriceService productPricingService;
        private readonly IOutletWalletService outletWalletService;
        private readonly IDSDPriceService dSDPriceService;
        private readonly ISchemeService schemeService;
        private readonly BlobHelper blobHelper;
        private readonly FileLogger _logger;
        private readonly IHccbIntegrationLogsRepository _hccbIntegrationRepository;
        private readonly QueueHandlerService _queueHandlerService;

        protected override string LogHeader { get; set; }
        protected override List<IntegratationSummary> _summary { get; set; } = new List<IntegratationSummary>();
        protected override List<EntityEnum> IntegrationEntities => new List<EntityEnum> { EntityEnum.PromotionMaster,EntityEnum.StockMaster, EntityEnum.PricingMasterDirect, EntityEnum.PricingMasterIndirect};
        private long companyId = Dependencies.CompanyId;
        private long integrationLogId = 0;
        private QueueModel? queueRequest = null;
        private FileInfo? logFile = null;
        private string _fileName = string.Empty;
        public HCCBDMSBlobFunction(
            IDistributorStockService distributorStockService,
            IProductPriceService productPricingService,
            IOutletWalletService outletWalletService,
            ISchemeService schemeService,
            BlobHelper blobHelper,
            ISlackLogHelper slackLogHelper,
            FileLogger logger,
            ISlackServiceProvider slackServiceProvider,
            IHttpClientFactory httpClientFactory,
            IDSDPriceService dSDPriceService,
            IHccbIntegrationLogsRepository hccbIntegrationRepository, QueueHandlerService queueHandlerService)
            : base(
                fileLogger: logger,
                slackLogHelper: slackLogHelper,
                hccbIntegrationRepository: hccbIntegrationRepository)
        {
            this.distributorStockService = distributorStockService;
            this.productPricingService = productPricingService;
            this.outletWalletService = outletWalletService;
            this.schemeService = schemeService;
            this.blobHelper = blobHelper;
            _logger = logger;
            this.dSDPriceService = dSDPriceService;
            _hccbIntegrationRepository = hccbIntegrationRepository;
            _queueHandlerService = queueHandlerService;
        }
        protected override string GetProcessorName() => "HCCB-DMS-Blob-Processor";
        protected override string GetChannelId() => Dependencies.ChannelId;
        protected override long GetCompanyId() => companyId;
        protected override IntegrationEnum GetIntegrationEnum() => IntegrationEnum.DMSBlobTriggeredJob;
        public async Task ProcessBlobFromFileName([QueueTrigger(Dependencies.HccbContainerName)] QueueModel request)
        {
            queueRequest = request;
            // Call the overridden Process method
            _fileName = request.FileName;
            await Process(queueRequest);
        }
        protected override (string, FileInfo) InitializeLogFile()
        {
            var fileName = queueRequest.FileName;
            var logFilePath = $"{LogDirectory}{Path.DirectorySeparatorChar}{fileName}_logs_{Guid.NewGuid()}.txt";
            logFile = new FileInfo(logFilePath);
            LogHeader = $"[{logFile.Name.Replace("_logs", "").Replace(".txt", "")}]: ";
            return (logFilePath, logFile);
        }

        protected override async Task _Process(params object?[] args)
        {
            var entityEnum = GetEntityEnum();
            if (entityEnum != null) {
                int index = (int)(entityEnum - EntityEnum.PromotionMaster);
                integrationLogId = GetIntegrationLog(IntegrationEntities[index]).Id;
            }
            await _ProcesBlob(_fileName);
        }

        protected override List<IntegrationLog> CreateIntegrationLogs()
        {
            return IntegrationEntities.Where(s => s == GetEntityEnum()).Select(s => new IntegrationLog
            {
                IntegrationEnum = GetIntegrationEnum(),
                IntegrationName = GetIntegrationEnum().ToString(),
                EntityEnum = s,
                EntityName = s.ToString(),
                CreatedAt = DateTime.UtcNow,
                LastUpdatedAt = DateTime.UtcNow,
                CreationContext = GetProcessorName(),
                CompanyId = GetCompanyId()
            }).ToList();
        }

        protected override async Task AddIntegrationLogs()
        {
            var entityEnum = GetEntityEnum();
            if (entityEnum == null) return;
            await _hccbIntegrationRepository.AddIntegrationLogs(logs);
        }
        private async Task _ProcesBlob(string name)
        {
            bool fileProcessed;
            (int totalRecords, int successfulRecords, int failedRecords) result;

            if (name.Contains("PromotionMaster_"))
            {
                result = await schemeService.Process(name, integrationLogId);
                _summary.Add(new IntegratationSummary
                {
                    TotalReceived = result.totalRecords,
                    Successful = result.successfulRecords,
                    Failed = result.failedRecords
                });
                fileProcessed = true;
                await TriggerHCCBSchemeSnapShotUpdate(name);
            }
            else if (name.Contains("StockMaster_"))
            {
                result = await distributorStockService.Process(name, integrationLogId);
                _summary.Add(new IntegratationSummary
                {
                    TotalReceived = result.totalRecords,
                    Successful = result.successfulRecords,
                    Failed = result.failedRecords
                });
                fileProcessed = true;
            }
            else if (name.Contains("PricingMaster_"))
            {
                bool isDirect = name.EndsWith("_D.zip", StringComparison.OrdinalIgnoreCase);
                if (isDirect)
                {
                    result = await dSDPriceService.Process(name, integrationLogId);
                    _summary.Add(new IntegratationSummary
                    {
                        TotalReceived = result.totalRecords,
                        Successful = result.successfulRecords,
                        Failed = result.failedRecords,
                        Ignored = 0
                    });
                }
                else
                {
                    result = await productPricingService.Process(name, integrationLogId);
                    _summary.Add(new IntegratationSummary
                    {
                        TotalReceived = result.totalRecords,
                        Successful = result.successfulRecords,
                        Failed = result.failedRecords
                    });
                }
                fileProcessed = true;
            }
            else if (name.Contains("Wallet_"))
            {
                await outletWalletService.Process(name);
                fileProcessed = true;
            }
            else if (name.Contains("PromotionMasterBundleSKU_"))
            {
                _logger.WriteLine($"{name}: Found bundle file. Skipping...");
                fileProcessed = false;
            }
            else
            {
                _logger.WriteLine($"{name}: Unrecognized file name. Archiving ...");
                await _slackLogHelper.SendMessageToSlack($"{name}: Unrecognized file name. Archiving ...");
                fileProcessed = true;
            }

            if (fileProcessed)
            {
                await ArchiveFile(blobHelper, _logger, name);
            }
        }

        private async Task TriggerHCCBSchemeSnapShotUpdate(string name)
        {
            await _queueHandlerService.AddStringToQueue(HCCBStorageQueues.SchemeSnapshotUpdateQueue, GetCompanyId().ToString());
        }

        public static async Task ArchiveFile(BlobHelper blobHelper, FileLogger logger, string name)
        {
            try
            {
                await blobHelper.ArchiveBlob(name);
            }
            catch (Exception ex)
            {
                logger.WriteLine($"{name}: Archiving blob failed with exception {ex.Message}");
            }
        }
        protected override void _ValidateArguments(params object?[] args)
        {
            return;
        }
        private EntityEnum? GetEntityEnum()
        {
            switch (_fileName)
            {
                case var n when n.Contains("PromotionMaster_"):
                    return EntityEnum.PromotionMaster;

                case var n when n.Contains("StockMaster_"):
                    return EntityEnum.StockMaster;

                case var n when n.Contains("PricingMaster_"):
                    return _fileName.EndsWith("_D.zip", StringComparison.OrdinalIgnoreCase)
                        ? EntityEnum.PricingMasterDirect
                        : EntityEnum.PricingMasterIndirect;
                default:
                    return null;
            }
        }
        protected override void UpdateLogs(List<IntegrationLog> logs)
        {
            int index = 0;
            foreach (var log in logs)
            {
                if (_summary?.Count > index)
                {
                    log.TotalReceived = _summary[index].TotalReceived;
                    log.TotalSuccess = _summary[index].Successful + _summary[index].Ignored;
                    log.TotalFailure = _summary[index].Failed;
                    log.LastUpdatedAt = DateTime.UtcNow;
                    log.FileName = _fileName;
                    ++index;
                }
                else
                {
                    return;
                }
            }
        }
    }
}
