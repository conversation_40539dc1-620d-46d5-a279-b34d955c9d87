﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;
using Library.MasterDb.DbModels;
using Newtonsoft.Json.Linq;

namespace Library.CommonServices.CompanySettings.Models;

public interface ICompanySettings
{
    JourneyPlanType GetJourneyPlanType { get; }

    TimeSpan TimeZoneOffset { get; }

    string Country { get; }

    bool UsesDistributorsOfRegion { get; }

    List<string> GetCompanyShopTypes { get; }

    bool UsesBeatPlans { get; }

    JourneyFrequency PJPFrequencyType { get; }

    bool UsesPJP { get; }

    bool UsesProductRegionalPricing { get; }

    bool CompanyUsesSkipODSFlow { get; }

    int MonthStartDate { get; }

    int YearStartMonth { get; }

    bool ShouldReverseGeocodeDayStarts { get; }

    bool IsUsingCallPrepStatsCalculation { get; }

    bool CompanySendsPricingInPrimaryInvoiceAPI { get; }

    bool RestrictTheVisibilityOfStockValuePresentInVan { get; }

    // Dictionary<string, object> GetAppSettingDictionary();
    List<string> GetOfficialWorkTypes { get; }

    List<string> GetCustomPaymentOptions { get; }

    TypeofDistributorMapping TypeofDistributorMapping { get; }

    bool UsesSalesInAmount { get; }

    bool UsesCloseToExpiry { get; }

    bool UsesConversionFactor { get; }

    bool CompanyUsesDMS { get; }

    bool UsesRetailerStock { get; }

    bool PurchasePriceInclusiveOfTax { get; }

    string DiscountType { get; }

    bool UsesDistributorCashDiscount { get; }

    List<string> GetUsesMustSellReason { get; }

    string ProductRecommendationRanking { get; }

    TypeofTaxCalculation TypeOfTaxCalculation { get; }

    List<string> GetReasonForNotTakingRetailerStock { get; }

    List<string> GetCityGrades { get; }

    List<string> GetReasonForProductReturn { get; }

    List<string> GetReasonForDamagedReturn { get; }

    List<string> GetReasonForExpiredReturn { get; }

    string CurrencySymbol { get; }

    int NoOfDaysForMarkingDeadOutlet { get; }

    bool TelephonicOrdersAvailable { get; }

    int MaxOutletInBeatOrRoute { get; }

    bool CompanyUsesAddOutletRequest { get; }

    bool UsesVanSales { get; }

    bool IsPrintingAvailable { get; }

    bool ShowShopAdditionScreen { get; }

    bool NewOutletCreationByOTP { get; }

    bool BlockOrderBookingIfNotPhysicallyPresent { get; }

    double MapInvalidDistanceLimit { get; }

    string NewOutletApprovalLevel { get; }

    string MinimumLevelOfApprovalForAttendanceRegularization { get; }

    int DayLimitforOrderDispatch { get; }

    List<string> GetReasonForJourneyDiversion { get; }

    bool IsUsesOutletMargin { get; }

    List<string> VanLoadinDiscrepencyRemarks { get; }

    bool IsOrderReviewAllowed { get; }

    bool UsesDistProdDivBeatMappings { get; }

    bool UsesAutomaticERPIDWithRegionCode { get; }

    bool UsesPositionCodes { get; }

    string CompanyUsesCreditLimit { get; }

    bool CompanyUsesMaximumStockNorm { get; }

    bool UsesTabularWhatsAppOrderFormat { get; }

    string DistributorStockType { get; }

    int MinimumDaysForTourPlan { get; }

    bool UsesIntelligentSchemes { get; }

    bool UsesSecondarySales { get; }

    bool UsesPrimaryOrder { get; }

    bool CallReviewAllowedForPrimaryOrder { get; }

    EmployeeTargetOn EmployeeTargetType { get; }

    TargetValueTypeDashboard TargetValueTypeDashboard { get; }

    string CompanyPricingModel { get; }

    string CompanyPricingType { get; }

    bool CompanyUsersCanSeeCasePriceInsteadOfMRP { get; }

    EmployeeTargetsCalculationType CalculateAchievementAgainstEmployeeTargets { get; }

    bool DuplicateOutletCheckOnline { get; }

    bool EnableExternalAssets { get; }

    bool CompanyUsesOutletReach { get; }

    bool UsesFocusProductTarget { get; }

    bool IsCompanyUsesTwentyMinutesAddress { get; }

    string FABattleGround { get; }

    OrderBookingScreenType AppOrderBookingScreenType { get; }

    DuplicateOutletCheck DuplicateOutletCheck { get; }

    CountryInfo CountryInfo { get; }

    JArray FAFloIntegrationSettings { get; }

    bool UsesTADA { get; }

    bool UsesFAFLO { get; }

    bool IsUsingLMS { get; }
    bool CompanyUsesLMS { get; }

    bool UsesIOSApp { get; }

    TargetOn TargetOn { get; }

    TargetOn BeatTargetOn { get; }

    bool UsesPaymentFeature { get; }

    bool CompanyUsesRegionWiseOutletWiseVerification { get; }

    string OutletWiseTarget { get; }

    double GSTTaxCalculationPercentage { get; }

    int NumberofDaysFastMovingCalculation { get; }

    bool UsesFAUnify { get; }

    bool DistributorWiseStyleLevelMarking { get; }

    List<string> GetPrimaryOrderType { get; }

    string UsesOutletVerification { get; }

    bool OrderatSuperStockist { get; }

    string CalculateAchFrom { get; }

    string ShowEmployeeSalesIn { get; }

    string ShowTimerOfficialWorkUpto { get; }

    bool SecondaryTarget { get; }

    string PrimaryTarget { get; }

    int DayLimitForPrimaryOrderDispatch { get; }

    bool RetailerOTPForOrderConfirmation { get; }

    bool IsOnlineDMS { get; }

    bool UsesEnterpriseFeatures { get; }

    bool UsesNewOutletCreationOTPAlphanumeric { get; }

    bool UsesDayStartSelfie { get; }

    bool IsOCREnable { get; }

    bool CompanyUsingDMSCollectionModule { get; }

    bool PlotJourneyforLevel1User { get; }

    bool UsesNewAttendanceModule { get; }

    bool UsesHighlightDormantSKU { get; }

    bool AllowSubDistributorCreationthroughApp { get; }

    bool GeofencingAtDistributorPoint { get; }

    bool IsUsingNewJourneyPlanStructure { get; }

    bool IsBeatForNewJourneyPlanStructure { get; }

    bool CompanyUsesPlannedLeave { get; }

    bool CompanyUsesSickLeave { get; }

    bool AllowSecondaryActivityInNEWJP { get; }

    bool CompanyUsesCasualLeave { get; }

    bool CompanyUsesLeaveManagement { get; }

    bool CompanyUsesMinimumOrderQtyForPrimaryOrders { get; }

    string SalesDataVisibilityMode { get; }

    bool CompanySharesStockDetailsInWhatsAppOrder { get; }

    bool CompanyUsesApprovalForAddingOutletToRoute { get; }

    int DaysUptoWhichUserCanTakeMonthlyDistributorStock { get; }

    bool UsesNewVanLoadoutFormat { get; }

    bool CompanyTakesPaymentAgainstPastVanOrders { get; }

    bool UsesAssetManagement { get; }

    bool AllowGeographicalMappingOfOutlets { get; }

    bool CompanyUsesDMSApprovalforVanLoadout { get; }

    bool CompanyUsesDMSApprovalforVanLoadin { get; }

    bool AutoApproveOutletVerificationRequest { get; }

    bool FetchOutletPerformancedatafrom3rdPartyDMS { get; }

    bool AllowUserToSelectDistributorOnOrderConfirmation { get; }

    bool UsersMustCompleteDistributorVisitBeforeRetailing { get; }

    bool NotifyManagerWhenDistributorStockIsTaken { get; }

    bool Uses7DayOrderDataForVanSale { get; }

    bool CompanyUsesCouponBasedLoyaltyScheme { get; }

    bool RestrictOrderValueMoreThanSegmentationPotential { get; }

    bool CompanyUsesKYCVerification { get; }

    bool CompanyUsesDistributorSKUMapping { get; }

    long CompanyKYCForm { get; }

    bool AllowReturnQuantityGreaterThanOrderQuantity { get; }

    bool ShowNoStockProductsForVanSales { get; }

    bool CompanyAllowsSkipOTPVerificationInODS { get; }

    bool UsesRegionalAdminApprovalForSubDistributor { get; }

    bool CompanyDoesNotUseGST { get; }

    bool Mandatorilycaptureinvoiceduringvalidation { get; }

    bool UsesAssetAuditing { get; }

    bool CompanyUsesStockNorm { get; }

    bool UsesAssetAllocation { get; }

    bool UsesAssetAgreement { get; }

    bool CompanyUsesProductRecommendation { get; }

    bool CompanyAllowsActionAgainstOlderInvoices { get; }

    int DaysUptoWhichEnablePrevMonthRegularization { get; }

    List<string> NoAssetAuditReasons { get; }

    bool AllowSkipOTPVerification { get; }

    bool CompanyShowsWarehouseStockInVanSales { get; }

    bool Showcarouselbannerinapp { get; }

    bool ShowingUsersWhoHaveNotSubmittedTourPlan { get; }

    bool CompanyUsesOptionalLoadInSettlement { get; }

    string CompanyUsesManagerApprovalForLoadOut { get; }

    bool CompanyUsesChannelWisePricing { get; }

    bool AllowUserToEditRoutePlanInApp { get; }

    bool CompanyUsesLoadOutOrderIntent { get; }

    bool HideWarehouseStock { get; }

    string AllowPrimaryOrderBookingInNSAppTo { get; }

    string Usercanbookprimaryorderfrom { get; }

    bool CompanyUsesReturnsInsteadofReplacementforVanSales { get; }

    bool editShopForbidden { get; }

    bool CompanyAllowsUserToEditOutletDetailsInApp { get; }

    bool SecondarySchemeonGrossValue { get; }

    bool CompanyUsesRouteOptimization { get; }

    bool AutomaticCombinationOfCityGrades { get; }

    int TaxIDLength { get; }

    bool RestrictOrderBookingIfTentativeStockIsZero { get; }

    bool CompanyAllowsUserDAEditing { get; }

    bool CompanyAllowsUserToDivertJourneyWithoutApproval { get; }

    bool UsesDifferentBillingAndShippingDetails { get; }

    int taxType { get; }

    bool IsDisablePreSalesforDSR { get; }

    bool IsAllowCompanyToFilterProductsWithZeroWarehouseStock { get; }

    bool CompanyHasMoreThanThresholdProduct { get; }

    List<string> ReasonsForPartialSOFulfilment { get; }

    bool CompanyUses3rdPartyAPIForCouponScheme { get; }

    bool CompanyUsesCueCards { get; }

    bool CompanyUsesSequentialInvoicingUsingSalesmanCode { get; }

    bool CompanyUsesBankMasterInPaymentCollection { get; }

    string DistanceCalculationMethodology { get; }

    string LocationCapturingDuration { get; }

    bool AllowUsersToSeeTentativeStock { get; }

    string DefaultDataPointerToBeShownBelowEachSKU { get; }

    bool CompanyUsesEInvoicing { get; }

    bool CompanyUsesEnhancedWhatsAppPDF { get; }

    string usercanbooksecondaryorderfrom { get; }

    bool CompanyUsesOpenMarketOperations { get; }

    bool CompanyUsesAttendanceBasedTADA { get; }

    bool IsCompanyUsingProductRecommendationEngine { get; }

    bool CompanyUsesAdditionalStockForVanSales { get; }

    string MultipleSchemesWithSameSKUs { get; }

    bool AllowDuplicationCheckBasedOnPDThroughCustomTags { get; }

    bool CompanyUsesCustomSequenceNumbers { get; }

    bool UsesthirdPartyAPIforInvoiceAchievement { get; }

    bool CompanyUsesAssetReallocation { get; }

    bool UsesJourneyCalendar { get; }

    bool AddOutletRequestWithOrderBooking { get; }

    bool ManagerDoesPrimaryOperationsInDistributorVisit { get; }

    bool ShowOutletStatsOnPrepScreen { get; }

    bool ShowOutletPreparationScreenDataInStandardUnit { get; }

    bool CollectFeedbackOnlyForUnsoldSKUs { get; }

    bool AutoGenerateAssetReferenceNumber { get; }

    string FeedbackCollectionForProductRecommendation { get; }

    List<string> FeedbackOptionsForProductReccomendation { get; }

    bool CompanyAllowsDSRToCollectPaymentForPreSales { get; }

    bool CompanyUsesLoadoutLoadinAutomation { get; }

    bool CompanyUses3rdBookingUnit { get; }

    List<string> CategoryProductivityKPICategories { get; }

    bool PurchasePriceInclusiveOfTaxInIndia { get; }

    bool CompanyAllowsReplacementAgainstDifferentProduct { get; }

    string CompanyAllowsPromotorToTakeInward { get; }

    bool JourneyDiversionApprovalOnlyToAdmin { get; }

    string CountOfNearBYBuyingStoreProductRecommendation { get; }

    bool IsCompanyUsingProductWinback { get; }

    bool CompanyAllowsDSRToEditLoadIn { get; }

    bool CompanyAllowsLoadoutAfterStockNormViolation { get; }

    bool CompanyUsesGSTInPrimaryOrder { get; }

    bool CompanyUsesFA_IR { get; }

    List<string> ImageCategoriesForIR { get; }

    bool usesLast10Invoices { get; }

    bool DistributorWisePricingInTelephonicOrder { get; }

    bool CompanyUsesSecondaryPJP { get; }

    bool Companydoesnotallowtocreatenewoutletsinpresale { get; }

    bool CompanyAllowsBatchManagementForVanSales { get; }

    string BeatTargetValueType { get; }

    bool AbleToTakeStockOnMustSell { get; }

    bool CompanyAllowsDistributorWiseLoadApproval { get; }

    bool ApparelCompanyUsesBasketsAndSets { get; }

    bool AllowUserToInputPOandDateOfDelivery { get; }

    bool CompanyDefinesPricingOnSuperStockistLevel { get; }

    long OpportunityTagId { get; }

    string AuthenticationForGSTAPI { get; }

    string APIForGSTVerification { get; }

    string CompanyUsesHandHeldPrinterType { get; }

    bool CompanyUsesMapView { get; }

    string UserCanTakeDistributorStockFrom { get; }

    string OutletMarginIsCalculatedBasedOn { get; }

    bool CompanyUsesMasterBatch { get; }

    bool SubOrdinateBeatsToBeShownTillHighestLevel { get; }

    bool CompanyUsesHCCBUserFlows { get; }

    bool CompanyUsesKPIBasedBeatometer { get; }

    bool UserAllowedToBookOrderWithoutDistributor { get; }

    bool CompanyUsesPositionPDMapping { get; }

    bool CompanyAllowsUserToTakeEveningLoadOut { get; }

    bool CompanyUsesTaskManagement { get; }

    bool CompanyUsesUserKPIDashboard { get; }

    bool CompanyUsesSubDGeographicalMapping { get; }

    bool CompanySettlesWithKeepInVan { get; }

    bool CompanyHasNonRouteChangeOVT { get; }

    bool CompanyUsesCustomerCategoryWisePricing { get; }

    bool CompanyDoesNotAllowDSRToRejectAdditionalLoadout { get; }

    bool CompanyRequiresAutoApprovedData { get; }

    bool CompanyAllowReordersInCaseOfReviewOrder { get; }

    bool CompanyUsesExciseDutyOnProducts { get; }

    bool CompanyUsesFlexibleTargetModule { get; }

    bool SchemeOnMasterandManufacturingBatch { get; }

    bool CompanyAllowMultipleDeviceLogin { get; }

    bool CompanyWantsToShowPowerBIInGTApp { get; }

    bool CompanyUsesSchemeApproval { get; }

    bool CompanyUsesSRWiseBudgetAllocation { get; }

    bool CompanyWantsToShowOrderStatusInApp { get; }

    bool CompanyBlocksOrderOnPreDefinedDates { get; }

    List<string> AssetStatusOptions { get; }

    bool EnableOrderRestriction { get; }

    bool CompanyDoesNotShowNonSaleableProductsOnLoadout { get; }

    bool CompanyAllowsLoadoutIntentAsLoadout { get; }

    bool CompanyUsesPreTaxOrPostTaxDiscount { get; }

    bool CompanyCreatesOutletWithoutOrderOrRequest { get; }

    bool IsCompanyUsesNormBasedAttendance { get; }

    string ProductQuantityAttributesForLoadout { get; }

    string UserAboveThisLevelChangeBeatWithoutApproval { get; }

    bool MakeAssetAuditingMandatory { get; }

    bool AllowOrderCancellation { get; }

    bool CompanyAllowsL1UserToCreateSubDistributorIntheApp { get; }

    bool CompanyRestrictsPrimaryOrderBookingInUnit { get; }

    bool CompanyAllowEditPTR { get; }

    bool CompanyUsesDisplayCategoryasSuperCode { get; }

    bool RestrictScreenRecording { get; }

    bool CompanyUsesGeofencingAtAddToRouteFeature { get; }

    bool CompanyUsesFATradeApp { get; }

    bool whatsappOrderToDistributor { get; }

    bool CompanyUsesMRPBatchBasedOrderBooking { get; }

    bool CompanyUsesOnlinePaymentMode { get; }

    bool CompanyAllowsSubDCreationByISR { get; }

    bool CompanyUsesPerfectStore { get; }

    bool CompanyFetchPrimaryVsSecondaryDataFrom3rdPartyDMS { get; }

    bool CompanyUsesEInvoicingInKenya { get; }

    bool NonPJPVisitLock { get; }

    bool CompanyMigratesToFlutter { get; }

    bool CompanyUsesPerfectStoreRewards { get; }

    bool CompanyUsesPreferredSchemes { get; }

    bool usesQPSScheme { get; }

    bool CompanyUsesFactorySelect { get; }

    bool EnableRuralMGRFlowGTforHCCB { get; }

    bool CompanyUsesAdvanceSearchFeature { get; }

    bool CompanyUsesHotsellingOutlets { get; }

    string CompanyUsesMetricCalculationType { get; }

    bool CompanyUsesDynamicBanner { get; }

    bool EnableLCTargetNudgeOnOrderBooking { get; }

    bool ShowDynamicFilterOnOutletListScreen { get; }

    bool CompanyUsesWarehouseManagement { get; }

    bool AllowSingleSelectFilterOnOrderBooking { get; }

    bool HideAddressOnOutletListScreen { get; }

    bool CompanyUsesOnlyGST { get; }

    bool CompanyCreatesPlanOnPositionCodes { get; }

    bool CompanyUsesSameDayOrderCancellation { get; }

    bool UserCanRaiseOrderCancellationRequest { get; }

    string CompanyUsesCallReviewDuration { get; }

    bool CompanyAllowMultipleBeatDayStart { get; }

    bool canSearchOutletsAcrossBeats { get; }

    bool AddOutletToRouteviaDuplicationCheck { get; }

    bool MandatoryRetailerStockcapture { get; }

    List<string> ReasonForOrderCancellation { get; }

    bool NewApprovalMechanism { get; }

    List<string> CompanyUsesProductDisclaimerText { get; }

    bool ShowDiscountAmountInFocScheme { get; }

    bool CompanyUsesFACare { get; }

    int GetThresholdPercentageForSchemeReco { get; }

    bool IsHccbIRLogicKey { get; }

    bool CompanyUsesSimplifiedSFAFlow { get; }

    int CompanyShowsNumberOfDecimalPlacesInApp { get; }

    bool CompanyUsesExtendedOutletInformation { get; }

    string OrderRestrictBasedOnOutletPotential { get; }

    bool ShowMRPAgainstEachProductInApparelUI { get; }

    bool CompanyUsesEmailVerification { get; }

    bool GulfGRSRequest { get; }

    bool EnablePANVerification { get; }

    int ConversionFactorForSecondaryCurrency { get; }

    bool DisableLocationFieldAfterAutoCapture { get; }

    bool ImplementLoggerForVanSales { get; }

    bool CompanyUsesDistributorStock { get; }

    int CompanyAllowsCurrencyOffsetUpto { get; }

    string TermsAndConditionsForVanSalesInvoicePrint { get; }

    bool ApplySchemesSuccessively { get; }

    double ConversionFactorForSecondaryCurrencyV2 { get; }

    string SupportEmail { get; }

    string SupportPhone { get; }

    int yearStartMonth { get; }

    string CompanyRequiresFollowingDataOnAllScreens { get; }

    bool UsesAutomatedTADA { get; }

    bool CompanyUsesManDaysCapping { get; }

    string ProductRecommendationSalesConsidered { get; }

    string EndUserFeedback { get; }

    bool CompanyUsesAdminFlowInAssetAllocation { get; }

    bool CompanyUsesTertiaryOrderFlow { get; }

    bool CompanyUsesProductVisibilityControlRule { get; }

    string CompanyUsesEmailNotification { get; }

    bool CompanyUseExternalAPIForDSRDayEnd { get; }

    bool CompanyUsesFACopilot { get; }

    bool ShowMTDBilledTagOnProducts { get; }

    bool CompanyUsesAdvanceLeave { get; }

    bool CompanyusesOutletRevisitFeature { get; }

    bool CompanyUsesTaxRecalculation { get; }

    bool showTimerOnOrderBooking { get; }

    bool ShowPreviousVisits { get; }

    bool CompanyusesEffective_PCKPI { get; }

    bool UsesAdvanceDeadOutletFlow { get; }

    bool FlexibleTargetVisibilityManagerAppOld { get; }

    bool AllowInvoicingOnDefaultPriceInCustomerCategory { get; }

    bool AssetMappingQRFlow { get; }

    bool FilterSurveysViaUserCohortInFlutter { get; }

    List<string> GetDistributorStockCapturedAllowedOnDates { get; }

    bool AllowBatchWiseOrderWithoutStock { get; }

    bool ShowPrimaryOrderHistory { get; }

    bool ShowPrimaryOrderInvoices { get; }

    bool CompanyUsesOutletDuplicateCheckDistributorWise { get; }

    List<string> GetNoDeliveryReasonsInDeliveryApp { get; }

    bool usesVanSales { get; }

    string UserFeedbackTriggerDate { get; }

    bool CompanyHasDirectDealers { get; }
    bool EnableBottlerCompanyFlows { get; }

    bool DistributorVisitAsDayStartType { get; }

    bool CompanyUsesProductOutletWiseCueCards { get; }

    bool OrderStatusValidationFromExternalAPI { get; }

    AppLockType CompanyUsesAppLockType { get; }

    string UserJourneyplancanbe { get; }

    bool CompanyUsesCartDraftFunctionality { get; }

    bool CompanyUsesEmpContactNumberUpdation { get; }

    bool CompanyUsesDisplayCategoryinAppForSuperCode { get; }

    bool CompanyusesNetValueinNonFAInvoices { get; }

    bool CompanyUsesPackSizeVisibility { get; }

    bool AllowUsersToSeeTentativeStockOffline { get; }

    int SurveyTriggerAfterHours { get; }

    bool CompanyUsesTheFlexibleTourPlanOrNot { get; }

    string GetEntityListForFlexibleTourPlan { get; }

    bool CustomNetValueinTodayPerformance { get; }

    bool CompanyUsesOfflineRetailerCollectionModule { get; }

    bool AllowUserToEditPriceInTheInvoicePostDelivery { get; }

    bool SendDistributorIrrespectiveOfToken { get; }

    bool CompanyUsesAssetAuditWithQRFlow { get; }

    bool AllowAccessToAllApps { get; }

    bool RestrictOrderBookingwhereBeatDBMappingisabsent { get; }

    bool CompanyUsesCreditBalanceForOutstandingCalculation { get; }

    bool CompanyAllowsPaymentCollectionForPresale { get; }

    bool CompanyAllowsPaymentCollectionAgainstDelivery { get; }

    bool CompanyUsesPrintingForPreSales { get; }

    bool CompanyUsesTaxVerification { get; }

    bool JwWithBarCode { get; }

    int MaxRecommendationsOnCopilot { get; }

    bool ShowLiveDistributorStock { get; }

    bool CompanyUsesSharePDFAfterOrderConfirmation { get; }

    bool CompanyWantToSeeTheCollectionHistory { get; }

    bool CompanyAllowsEmptyManagementInApp { get; }

    int MaxDistributorIdsPerRequest { get; }

    string CompanyHasPrimaryVanReconciliationOperationAs { get; }

    string CompanyActionOnVanCapacityBreach { get; }

    double IncrementalValueForCashDiscount { get; }

    bool HideProductsWithPTRZeroinApp { get; }

    bool CompanyUserEitherCashORNeftAsModeOfPayment { get; }

    int CollectionAllowedForLastNDaysInvoices { get; }

    bool CompanyUsesRetailerCollection { get; }
    bool CompanyUsesPricingMaster { get; }
    bool OptimizedFlowForL3AndAbove { get; }

    bool DisableSyncRetailerCollectionsToRetailerLedger { get; }

    bool CompanyUsesDirectLoadoutFromDMS { get; }

    string OpenMarketStockRequestApprovalIsDoneVia { get; }
}
