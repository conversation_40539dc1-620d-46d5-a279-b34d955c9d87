﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Library.MasterDb.DbModels;
using Library.MasterDb.DbModels.FieldAssist.DataAccessLayer.Models.EFModels;
using Library.SMSHelpers;
using Microsoft.EntityFrameworkCore;

namespace Library.MasterDb.DbContexts
{
    public class ReadOnlyMasterDbContext : Microsoft.EntityFrameworkCore.DbContext
    {
        public ReadOnlyMasterDbContext(DbContextOptions<ReadOnlyMasterDbContext> options)
            : base(options)
        {
        }

        // MasterDBModels
        public virtual DbSet<ProductRegionalMeta> ProductRegionalMetas { get; set; }

        public DbSet<IDSLogins.IdsLogin> IDSLogins { get; set; }

        public DbSet<CouponsDefinition> CouponsDefinitions { get; set; }

        public DbSet<BeatCapping> BeatCappings { get; set; }

        public virtual DbSet<CouponScheme> CouponSchemes { get; set; }

        public virtual DbSet<CouponSchemeSlab> CouponSchemeSlabs { get; set; }

        public virtual DbSet<CouponMaster> CouponMasters { get; set; }

        public DbSet<SchemeCouponDistribution> SchemeCouponDistributions { get; set; }

        public DbSet<DistributorVanStockNorm> DistributorVanStockNorms { get; set; }

        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }

        public DbSet<EmployeeToken> ClientEmployeeTokens { get; set; }

        public DbSet<Employee> Employees { get; set; }

        public DbSet<Designations> Designations { get; set; }

        public DbSet<NewDistributorFieldsAppMeta> NewDistributorFieldsAppMetas { get; set; }

        public DbSet<BeatPlanItem> BeatPlanItems { get; set; }

        public DbSet<NoSalesReasonItem> NoSalesReasonItems { get; set; }

        public DbSet<CompanySetting> CompanySettings { get; set; }

        public DbSet<CompanySettingValue> CompanySettingValues { get; set; }

        public DbSet<LocationBeat> LocationBeats { get; set; }

        public DbSet<RegionWiseODSDB> RegionWiseODS { get; set; }

        public DbSet<GeographicalMapping> GeographicalMappings { get; set; }

        public DbSet<LocationBeatMapping> LocationBeatMappings { get; set; }

        public DbSet<ProductDivision> ProductDivisions { get; set; }

        public DbSet<ProductDisplayCategory> ProductDisplayCategories { get; set; }

        public DbSet<DistributorBeatMappingDB> DistributorBeatMappings { get; set; }

        public DbSet<DistributorRouteMapping> DistributorRouteMapping { get; set; }

        public virtual DbSet<DistributorProductMapping> DistributorProductMappings { get; set; }

        public DbSet<DistributorFieldUserMapping> DistributorFieldUserMappings { get; set; }

        public DbSet<DistributorProductDivisionMappingDB> DistributorProductDivisionMappings { get; set; }

        public DbSet<LocationDB> Locations { get; set; }

        public DbSet<Territory> Territories { get; set; }

        public DbSet<Zone> Zones { get; set; }

        public DbSet<Device> Devices { get; set; }

        public DbSet<Device_New> Devices_New { get; set; }

        public DbSet<BeatPlan> FABeatPlans { get; set; }

        public DbSet<EmployeePjp> EmployeePJPs { get; set; }

        public DbSet<PjpOutletMapping> PJPOutletMappings { get; set; }

        public DbSet<NewOutletFieldsAppMeta> NewOutletFieldsAppMetas { get; set; }

        public DbSet<StateWithDistrict> StateWithDistricts { get; set; }

        public DbSet<ProductSuggestiveQuantity> ProductSuggestiveQuantity { get; set; }

        public DbSet<CompanyDefinedProductType> CompanyDefinedProductTypes { get; set; }

        public DbSet<StockistStock> StockistStocks { get; set; }

        public DbSet<StockistStockItem> StockistStockItems { get; set; }

        public DbSet<PinCodeMaster> PinCodeMaster { get; set; }

        public DbSet<ProductChannelMapping> ProductChannelMappings { get; set; }

        public DbSet<CompanyFactories> CompanyFactories { get; set; }

        public DbSet<DistributorFactoryMappings> DistributorFactoryMappings { get; set; }

        public DbSet<ThemeConfig> ThemeConfig { get; set; }

        // CoreModels
        public DbSet<Company> Companies { get; set; }

        public DbSet<OutletSegmentationAttribute> OutletSegmentationAttributes { get; set; }

        public DbSet<Product> Products { get; set; }

        public DbSet<Survey> Surveys { get; set; }

        public DbSet<Question> Questions { get; set; }

        public DbSet<Choice> Choices { get; set; }

        public DbSet<QuestionGroup> QuestionGroups { get; set; }

        public DbSet<SurveyToCompanyZoneMap> SurveyToCompanyZoneMaps { get; set; }

        public DbSet<ProductPrimaryCategory> ProductPrimaryCategories { get; set; }

        public DbSet<CompanyAppMeta> CompanyAppMetas { get; set; }

        // latest development to get soft or hard update version for the specified platform(ios, android etc)
        public DbSet<CompanyAppMetadata> CompanyAppMetadata { get; set; }

        public DbSet<EmployeeProductDivisionMapping> EmployeeProductDivisionMappings { get; set; }

        public DbSet<NoSalesReason> NoSalesReasons { get; set; }

        public DbSet<CountryInfo> CountryDetails { get; set; }

        public DbSet<Region> Regions { get; set; }

        public DbSet<Distributor> Distributors { get; set; }

        public DbSet<EmployeeDayStartRecord> EmployeeDayStartRecords { get; set; }

        public DbSet<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }

        public DbSet<EmployeeBeatMapping> EmployeeBeatMappings { get; set; }

        public DbSet<EmployeeRouteMapping> EmployeeRouteMappings { get; set; }

        public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMappings { get; set; }

        public DbSet<CompanyNomenclature> CompanyNomenclatures { get; set; }

        public DbSet<ZonalProductMeta> ZonalProductMetas { get; set; }

        public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }

        public DbSet<EmployeeTourPlanItemSecondary> EmployeeTourPlanItemsSecondary { get; set; }

        public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }

        public DbSet<EmployeeDailyTargets> EmployeeDailyTargets { get; set; }

        public DbSet<EmployeeDailyTargetItems> EmployeeDailyTargetItems { get; set; }

        public DbSet<RoutePlan> RoutePlans { get; set; }

        public DbSet<RoutePlanItem> RoutePlanItems { get; set; }

        public DbSet<SecondaryRoutePlanItem> SecondaryRoutePlanItems { get; set; }

        public DbSet<RoutePlanRequest> RoutePlanRequests { get; set; }

        public DbSet<RoutePlanRequestItem> RoutePlanRequestItems { get; set; }

        public DbSet<Route> Routes { get; set; }

        public DbSet<RouteOutletMapping> RouteOutletMappings { get; set; }

        public DbSet<Scheme> Schemes { get; set; }

        public DbSet<CompanyAdmin> CompanyAdmins { get; set; }

        public DbSet<InternationalDiscount> InternationalDiscounts { get; set; }

        public DbSet<SchemeSlab> SchemeSlabs { get; set; }

        public DbSet<QualifierSchemeSlab> QualifierSchemeSlabs { get; set; }

        public DbSet<SchemeBasket> SchemeBuckets { get; set; }

        public DbSet<OfficialWorkTypeHierarchyMapping> OfficialWorkTypeHierarchyMappings { get; set; }

        public DbSet<OfficialWorkTypes> OfficialWorkTypes { get; set; }

        public DbSet<ShopType> ShopTypes { get; set; }

        public DbSet<Channel> Channels { get; set; }

        public DbSet<MarginSlab> MarginSlab { get; set; }

        public DbSet<EntityMarginSlab> EntityMarginSlabs { get; set; }

        public DbSet<FAPendingPayment> FAPendingPayments { get; set; }

        public DbSet<FAPendingPaymentInvoice> FAPendingPaymentInvoices { get; set; }

        public DbSet<Team> Teams { get; set; }

        public DbSet<TeamUserMapping> TeamUserMappings { get; set; }

        public DbSet<KPI> KPIs { get; set; }

        public DbSet<CompanyKPI> CompanyKPIs { get; set; }

        public DbSet<Game> Games { get; set; }

        public DbSet<GameAlert> GameAlert { get; set; }

        public DbSet<TargetForTeams> TargetForTeams { get; set; }

        public DbSet<CoinsforKpi> CoinsforKpis { get; set; }

        public DbSet<AssetDefinition> AssetDefinitions { get; set; }

        public DbSet<AssetOutletMapping> AssetOutletMappings { get; set; }

        public DbSet<AssetTypes> AssetTypes { get; set; }

        public DbSet<QuantityPurchaseScheme> QuantityPurchaseSchemes { get; set; }

        public DbSet<ExternalAsset> ExternalAssets { get; set; }

        public DbSet<SMSConfiguration> SMSConfigurations { get; set; }

        public DbSet<GSTCategoryTax> GSTCategoryTaxes { get; set; }

        public DbSet<ProductGSTCategory> ProductGSTCategories { get; set; }

        public DbSet<KPISlab> KPISlabs { get; set; }

        public DbSet<VanDistributorMapping> VanDistributorMapping { get; set; }

        public DbSet<VanMaster> VanMaster { get; set; }

        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }

        public DbSet<PositionCodeHierarchy> PositionCodeHierarchies { get; set; }

        public DbSet<PositionCode> PositionCodes { get; set; }

        public DbSet<PositionBeatMapping> PositionBeatMapping { get; set; }

        public DbSet<PositionProductDivisionMapping> PositionProductDivisionMappings { get; set; }

        public DbSet<RoutePositionMapping> RoutePositionMapping { get; set; }

        public DbSet<PositionDistributorMapping> PositionDistributorMappings { get; set; }

        public DbSet<ProductPricingMasters> ProductPricingMasters { get; set; }

        public DbSet<ProductPricingMastersV2> ProductPricingMastersV2 { get; set; }

        public DbSet<ExternalApiToken> ExternalApiTokens { get; set; }

        public DbSet<CarouselBanners> CarouselBanners { get; set; }

        public DbSet<PrimarySale> PrimarySales { get; set; }

        public DbSet<PrimarySaleItems> PrimarySaleItems { get; set; }

        public DbSet<DistributorWiseOutletCreditLimit> DistributorWiseOutletCreditLimit { get; set; }

        public virtual DbSet<BilledtoshippedtoAddress> BilledtoshippedtoAddress { get; set; }

        public DbSet<DanoneRunningNumbers> DanoneRunningNumbers { get; set; }

        public DbSet<OutletTag> OutletTags { get; set; }

        public DbSet<DistributorAddress> DistributorAddresses { get; set; }

        public DbSet<ProductCESSCategory> ProductCESSCategories { get; set; }

        public DbSet<CESSCategoryTax> CESSCategoryTaxes { get; set; }

        public DbSet<JourneyCalendar> JourneyCalendars { get; set; }

        public DbSet<JourneyCycle> JourneyCycles { get; set; }

        public DbSet<JourneyWeek> JourneyWeeks { get; set; }

        public DbSet<JourneyPlanConfigurations> JourneyPlanConfigurations { get; set; }

        public DbSet<OutletMetric> OutletMetrices { get; set; }

        public DbSet<OutletwiseExternalMetricValues> OutletwiseExternalMetricValues { get; set; }

        public DbSet<GlobalOutletMetrices> GlobalOutletMetrices { get; set; }

        public DbSet<FAProductBasket> ProductBasket { get; set; }

        public DbSet<FABasketProductMappings> BasketProductMappings { get; set; }

        public DbSet<CompanyNomenclature> CompanyNomenclature { get; set; }

        public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMapping { get; set; }

        public DbSet<FAProductSet> FAProductSet { get; set; }

        public DbSet<DistributorToRetailerMargins> DistributorToRetailerMargins { get; set; }

        public DbSet<CompanyTargetSubscriptions> CompanyTargetSubscriptions { get; set; }

        public DbSet<CompanyTargets> CompanyTargets { get; set; }

        public DbSet<BeatometerRuleDetails> BeatometerRuleDetails { get; set; }

        public DbSet<TargetMaster> TargetMaster { get; set; }

        public DbSet<FocusedProductRule> FocusedProductRules { get; set; }

        public DbSet<FocusedProductRulePositionCodeMapping> FocusedProductRulePositionCodeMappings { get; set; }

        public DbSet<CueCardsMaster> CueCardsMasters { get; set; }

        public DbSet<TaskManagementFocusArea> TaskManagementFocusAreas { get; set; }

        public DbSet<TaskManagementUserFocusArea> TaskManagementUserFocusAreas { get; set; }

        public DbSet<TaskManagementTask> TaskManagementTasks { get; set; }

        public DbSet<CompanyExternalMetrices> CompanyExternalMetrices { get; set; }

        public DbSet<EquipmentMaster> EquipmentMaster { get; set; }

        public DbSet<Reward> FARewards { get; set; }

        public DbSet<ImageRecognitionLogic> ImageRecognitionLogics { get; set; }

        public DbSet<ExternalLoginAccess> ExternalLoginAccesses { get; set; }

        public DbSet<UserWiseSchemeBudget> UserWiseSchemeBudget { get; set; }

        public DbSet<OrderBlock> OrderBlock { get; set; }

        public DbSet<OrderBlockItems> OrderBlockItems { get; set; }

        public DbSet<OutletUpdationRequest> FAOutletUpdationRequests { get; set; }

        public DbSet<EmployeeAdvanceLeave> EmployeeAdvanceLeaves { get; set; }

        public DbSet<AdvanceLeaveSubmission> AdvanceLeaveSubmissions { get; set; }

        public DbSet<DeviceConnection> DeviceConnections { get; set; }

        public DbSet<DeviceToken> DeviceTokens { get; set; }

        public DbSet<BiReportSubscriptions> BiReportSubscriptions { get; set; }

        public DbSet<PerfectStoreRule> PerfectStoreRules { get; set; }

        public DbSet<CompanyFactoryStocks> CompanyFactoryStocks { get; set; }

        public DbSet<ProductTagMaster> ProductTagMasters { get; set; }

        public DbSet<ProductTagSuggestion> ProductTagSuggestions { get; set; }

        public DbSet<DynamicCarouselBanner> DynamicCarouselBanners { get; set; }

        public DbSet<Cohort> Cohorts { get; set; }

        public DbSet<OutletOnboardingDetails> OutletOnboardingDetails { get; set; }

        public DbSet<DistributorSegmentations> DistributorSegmentations { get; set; }

        public DbSet<DistributorChannels> DistributorChannels { get; set; }

        public DbSet<RequestApprovalTimeline> RequestApprovalTimeline { get; set; }

        public DbSet<RequestApprovalRules> RequestApprovalRules { get; set; }

        public DbSet<LocationPointers> LocationPointers { get; set; }

        public DbSet<RoutePositionMapping> RoutePositionMappings { get; set; }

        public DbSet<ManagerToken> FAManagersTokens { get; set; }

        public DbSet<EngageNotification> FaEngageNotificationMasters { get; set; }

        public DbSet<NotificationMessage> FaEngageNotificationMessages { get; set; }

        public DbSet<QuantityScheme> QuantitySchemes { get; set; }

        public DbSet<ReportUseDetails> ReportUseDetails { get; set; }

        public virtual DbSet<EmailSchedule> EmailSchedules { get; set; }

        public DbSet<DumpReportRequest> DumpReportRequests { get; set; }

        public DbSet<ReportSubscription> ReportSubscriptions { get; set; }

        public DbSet<FocusedProductTarget> FocusedProductTargets { get; set; }

        public DbSet<GameIncentive> GameIncentives { get; set; }

        public DbSet<GameIncentiveSlab> GameIncentiveSlabs { get; set; }

        public DbSet<AssortedProductRule> AssortedProductRules { get; set; }

        public DbSet<ShopTypesForAssortedProductRule> ShopTypesForAssortedProductRules { get; set; }

        public DbSet<SamplingProduct> FASamplingProductSales { get; set; }

        public DbSet<FAProductRecommendationLogic> FAProductRecommendationLogics { get; set; }

        public DbSet<FAProductWinBackLogic> FAProductWinBackLogics { get; set; }

        public DbSet<SegmentationForAssortedProductRule> SegmentationForAssortedProductRules { get; set; }

        public DbSet<AssortedProductSKUDetails> AssortedProductSKUDetails { get; set; }

        public DbSet<AttendanceNormPolicy> AttendanceNormPolicies { get; set; }

        public DbSet<BeatometerRule> BeatometerRule { get; set; }

        public DbSet<CFRetailerInvoiceData> CFRetailerInvoiceDatas { get; set; }

        public DbSet<FaEngageKPITrigger> FaEngageKPITriggers { get; set; }

        public DbSet<DerivedKPI> DerivedKPIs { get; set; }

        public DbSet<UserUIPreference> UserUIPreferences { get; set; }

        public DbSet<OutletCreationRequest> OutletCreationRequests { get; set; }

        public DbSet<ChartVizDetails> ChartVizDetails { get; set; }

        public DbSet<QueryView> QueryViews { get; set; }

        public DbSet<OutletReachRule> OutletReachRules { get; set; }

        public DbSet<ProductBatchMaster> ProductBatches { get; set; }

        public DbSet<EmployeeTargetV2> EmployeeTargetV2 { get; set; }

        public DbSet<CustomReport> CustomReports { get; set; }

        public DbSet<CustomReportItem> CustomReportItems { get; set; }

        public DbSet<UserWiseCustomReport> UserWiseCustomReports { get; set; }

        public DbSet<UserWiseCustomReportItem> UserWiseCustomReportItems { get; set; }

        public DbSet<Geographies> Geographies { get; set; }

        public DbSet<PmsRule> PmsRules { get; set; }

        public DbSet<PmsRuleMetricConstraint> PmsRuleMetricConstraints { get; set; }

        public DbSet<AreaSalesManager> AreaSalesManagers { get; set; }

        public DbSet<RegionalSalesManager> RegionalSalesManagers { get; set; }

        public DbSet<ZonalSalesManager> ZonalSalesManager { get; set; }

        public DbSet<NationalSalesManager> NationalSalesManager { get; set; }

        public DbSet<GlobalSalesManager> GlobalSalesManager { get; set; }

        public DbSet<ConcurrentRequest> ConcurrentRequests { get; set; }

        public DbSet<ISROutletMapping> ISROutletMappings { get; set; }

        public DbSet<ManagerAppPointerMappings> ManagerAppPointerMappings { get; set; }

        public DbSet<KRATargets> KRATargets { get; set; }

        public DbSet<KRAMapping> KRAMappings { get; set; }

        public DbSet<UserTrainingTasksMaster> UserTrainingTasksMaster { get; set; }

        public DbSet<UserTrainingProgress> UserTrainingProgress { get; set; }

        public DbSet<Holidays> Holidays { get; set; }

        public DbSet<E11SuggestiveOrder> E11SuggestiveOrders { get; set; }

        public DbSet<CompanyKRATarget> CompanyKRATargets { get; set; }

        public DbSet<FACompanyModulesMapping> FACompanyModulesMapping { get; set; }

        public DbSet<OutletAdditionalDetails> OutletAdditionalDetails { get; set; }

        public DbSet<FAExtendedEntityAttributes> EntityAttributes { get; set; }

        public DbSet<OrderSummaryPDFConfigs> OrderSummaryPDFConfigs { get; set; }

        public DbSet<SecondaryOrderSummaryPdfConfigs> SecondaryOrderSummaryPdfConfigs { get; set; }

        public DbSet<VanOrderPdfConfigs> VanOrderPdfConfigs { get; set; }

        public DbSet<FlexibleReport> FlexibleReports { get; set; }

        public DbSet<TertiaryEntity> TertiaryEntities { get; set; }

        public DbSet<TertiaryEntityOutletMapping> TertiaryEntityOutletMapping { get; set; }

        public DbSet<ProductVisibilityRule> ProductVisibilityRules { get; set; }

        public DbSet<ApprovalRequest> ApprovalRequests { get; set; }

        public DbSet<NewApprovalEntities> ApprovalEntities { get; set; }

        public DbSet<NewApprovalEntityConfig> ApprovalEntityConfig { get; set; }

        public DbSet<EffectivePcKpiRule> EffectivePcKpiRules { get; set; }

        public DbSet<FilterConstraintDetail> FilterConstraintDetails { get; set; }

        public DbSet<RegexMaster> RegexMaster { get; set; }

        public DbSet<AnalyticsReportRequest> AnalyticsReportRequests { get; set; }

        public DbSet<PerfectEntityRule> PerfectEntityRules { get; set; }

        public DbSet<PerfectEntityRuleCriteria> PerfectEntityRuleCriterias { get; set; }

        public DbSet<PerfectCriteriaSlabDetail> PerfectCriteriaSlabDetails { get; set; }

        public DbSet<PerfectEntityQualifier> PerfectEntityQualifiers { get; set; }

        public DbSet<FAProductGroup> FAProductGroups { get; set; }

        public DbSet<TargetEntityQueries> TargetEntityQueries { get; set; }

        public DbSet<EmptyMaster> EmptyMaster { get; set; }

        public DbSet<ProductToEmptyMapping> ProductToEmptyMapping { get; set; }

        public DbSet<TaskManagementSoldProductQuantity> TaskManagementSoldProductQuantities { get; set; }

        public DbSet<PrimaryOrderPdfConfig> PrimaryOrderPdfConfigs { get; set; }

        public DbSet<SubShopType> SubShopTypes { get; set; }

        public DbSet<FABuyerSellerCredit> FABuyerSellerCredit { get; set; }
        public DbSet<LMSAccount> LMSAccounts { get; set; }
        public DbSet<LMSAccountContact> LMSAccountContacts { get; set; }
        public DbSet<LMSAccountAddress> LMSAccountAddresses { get; set; }
        public DbSet<LMSAccountTemplate> LMSAccountTemplates { get; set; }
        public DbSet<LMSAccountNote> LMSAccountNotes { get; set; }
        public DbSet<LMSCompanyLeadStage> LMSCompanyLeadStages { get; set; }
        public DbSet<LMSCompanyLeadTemplate> LMSCompanyLeadTemplates { get; set; }
        public DbSet<LMSGlobalLeadStage> LMSGlobalLeadStages { get; set; }
        public DbSet<LMSGlobalLeadTemplate> LMSGlobalLeadTemplates { get; set; }
        public DbSet<LMSLead> LMSLeads { get; set; }
        public DbSet<LMSLeadContact> LMSLeadContacts { get; set; }
        public DbSet<LMSLeadSource> LMSLeadSources { get; set; }
        public DbSet<LMSCustomField> LMSCustomFields { get; set; }
        public DbSet<LMSCustomFieldValue> LMSCustomFieldValues { get; set; }

        public DbSet<DistributorWiseTaxIntegration> DistributorWiseTaxIntegration { get; set; }

        public DbSet<GTFeature> GTFeatures { get; set; }
        public DbSet<GTFeatureMetric> GTFeatureMetrics { get; set; }
        public DbSet<CompanyWiseGTFeatureMetric> CompanyWiseGTFeatureMetrics { get; set; }
        public DbSet<TaxIntegrationDevice> TaxIntegrationDevice { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DistributorWiseOutletCreditLimit>().ToTable("DistributorWiseOutletCreditLimit");
            modelBuilder.Entity<CompanyAppMeta>().ToTable("FACompaniesAppMeta");
            modelBuilder.Entity<CompanyAppMetadata>().ToTable("FACompanyAppMetadata");
            modelBuilder.Entity<CompanyNomenclatureMapping>().ToTable("FACompanyNomenclaturesMapping");
            modelBuilder.Entity<CompanyNomenclature>().ToTable("FACompanyNomenclatures");
            modelBuilder.Entity<EmployeeBeatMapping>().ToTable("FAEmployeeBeatMappings");
            modelBuilder.Entity<EmployeeRouteMapping>().ToTable("FAEmployeeRouteMappings");
            modelBuilder.Entity<Distributor>().ToTable(
                "FADistributors",
                t => t.HasTrigger("FADistributorsAfterUpdate"));
            modelBuilder.Entity<RegionWiseODSDB>().ToTable("RegionWiseODS");
            modelBuilder.Entity<DistributorToRetailerMargins>().ToTable("DistributorToRetailerMargins");

            // for product
            modelBuilder.Entity<Product>().ToTable("FACompanyProducts");
            modelBuilder.Entity<FAProductBasket>().ToTable("FAProductBasket");
            modelBuilder.Entity<FABasketProductMappings>().ToTable("FABasketProductMappings");
            modelBuilder.Entity<FAProductSet>().ToTable("FAProductSet");
            modelBuilder.Entity<BeatCapping>().ToTable("BeatCappings");

            // managers
            modelBuilder.Entity<ProductPrimaryCategory>().ToTable("FAProductPrimaryCategory");
            modelBuilder.Entity<ProductSecondaryCategory>().ToTable("FAProductSecondaryCategory");
            modelBuilder.Entity<ProductDivision>().ToTable("FAProductDivision");

            modelBuilder.Entity<SurveyToCompanyZoneMap>().ToTable("FASurveyToCompanyZoneMaps");
            modelBuilder.Entity<Survey>().ToTable("NewSurvey_JsonForm");
            modelBuilder.Entity<QuestionGroup>().ToTable("NewSurvey_QuestionGroup");
            modelBuilder.Entity<Question>().ToTable("NewSurvey_Question");
            modelBuilder.Entity<Choice>().ToTable("NewSurvey_QuestionChoice");

            modelBuilder.Entity<CountryInfo>().HasKey(b => b.CountryName);

            // Ignore Outlets from mapping.
            modelBuilder.Entity<Survey>().Ignore(b => b.Outlets);

            // Task-Management
            modelBuilder.Entity<TaskManagementUserFocusArea>()
                .HasOne(ufa => ufa.TaskManagementFocusAreas)
                .WithOne(fa => fa.TaskManagementUserFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder.Entity<TaskManagementFocusArea>()
                .HasOne(fa => fa.TaskManagementUserFocusAreas)
                .WithOne(ufa => ufa.TaskManagementFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder.Entity<TaskManagementTask>()
                .HasOne(tt => tt.TaskManagementFocusAreas)
                .WithMany(fa => fa.TaskManagementTasks)
                .HasForeignKey(tt => tt.TaskManagementFocusAreaID);

            modelBuilder.Entity<NoSalesReason>()
                .HasMany(nsr => nsr.NoSalesReasonItems)
                .WithOne(nri => nri.NoSalesReason)
                .HasForeignKey(nri => nri.NoSalesReasonId);

            modelBuilder.Entity<FocusedProductRule>().ToTable("FAFocusedProductRule");

            modelBuilder.Entity<PmsRule>()
            .HasMany(p => p.PmsRuleMetricConstraints);

            modelBuilder.Entity<PmsRuleMetricConstraint>()
                .HasOne(p => p.GlobalOutletMetrices);

            modelBuilder.Entity<CompanyKRATarget>().ToTable("FACompanyKRATargets");
            modelBuilder.Entity<FocusedProductTarget>().ToTable("FocussedProductTargets");
        }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("The context is readonly");
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new InvalidOperationException("The context is readonly");
        }
    }
}
