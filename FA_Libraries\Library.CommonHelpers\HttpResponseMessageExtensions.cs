﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Library.CommonHelpers.HttpClients;
using Newtonsoft.Json;
using Opw.HttpExceptions;

namespace Library.CommonHelpers;

public static class HttpResponseMessageExtensions
{
    public static async Task EnsureSuccessStatusCodeAsync(this HttpResponseMessage response, CancellationToken token = default, bool logRequestContent = false, int logRequestContentLimit = 0)
    {
        if (response.IsSuccessStatusCode)
        {
            return;
        }

        var uri = response.RequestMessage?.RequestUri?.AbsoluteUri;
        var requestContentObject = response.RequestMessage?.Content;
        var responseContent = await GetResponseContent(response, token);
        var requestContent = await GetRequestContent(requestContentObject, logRequestContent, logRequestContentLimit, token);

        response.Dispose();

        throw new HttpException(response.StatusCode,
            $"Error in Http Request for url: {uri}\nRequest: {requestContent}\nResponse: {responseContent}");
    }

    private static async Task<string> GetRequestContent(HttpContent requestContentObject, bool logRequestContent, int logRequestContentLimit, CancellationToken token = default)
    {
        var requestContent = string.Empty;
        if (logRequestContent && requestContentObject != null)
        {
            if (requestContentObject is MultipartFormDataContent multipart)
            {
                foreach (var part in multipart)
                {
                    var disposition = part.Headers.ContentDisposition;
                    var contentType = part.Headers.ContentType?.MediaType?.ToLowerInvariant();
                    // Skip files/non textual content
                    if (string.IsNullOrEmpty(disposition?.FileName) && IsContentHumanReadable(contentType))
                    {
                        var name = disposition?.Name?.Trim('"');
                        var fieldValue = await part.ReadAsStringAsync();
                        requestContent += $"-----{name}----\n{fieldValue}\n";
                    }
                    else
                    {
                        requestContent += "[binary or unsupported content skipped]";
                    }
                }
            }
            else
            {
                // Skip files/non textual content
                var contentType = requestContentObject.Headers.ContentType?.MediaType?.ToLowerInvariant();
                if (IsContentHumanReadable(contentType))
                {
                    requestContent += await requestContentObject.ReadAsStringAsync(token);
                }
                else
                {
                    requestContent += "[binary or unsupported content skipped]";
                }
            }

            if (logRequestContentLimit > 0 && requestContent.Length > logRequestContentLimit)
            {
                requestContent = requestContent.Substring(0, logRequestContentLimit) + "...[truncated]";
            }
        }
        else
        {
            requestContent += "Not Logged";
        }

        return requestContent;
    }

    private static async Task<string> GetResponseContent(HttpResponseMessage response, CancellationToken token = default)
    {
        var responseContent = await response.Content.ReadAsStringAsync(token);

        // Truncate long response content
        const int maxResponseLength = 2000;
        if (responseContent.Length > maxResponseLength)
        {
            responseContent = responseContent.Substring(0, maxResponseLength) + "...[truncated]";
        }

        return responseContent;
    }

    private static bool IsContentHumanReadable(string contentType)
    {
        return contentType == null ||
            contentType.StartsWith("text/") ||
            contentType == "application/json" ||
            contentType == "application/xml" ||
            contentType == "application/x-www-form-urlencoded";
    }

    public static async Task<T> GetParsedJsonAsync<T>(this HttpResponseMessage response, CancellationToken token = default)
    {
        await response.EnsureSuccessStatusCodeAsync(token);
        using var reader = new StreamReader(await response.Content.ReadAsStreamAsync(token));
        await using var jsonReader = new JsonTextReader(reader);
        var ser = new JsonSerializer();
        return ser.Deserialize<T>(jsonReader);
    }

    public static async Task<T> GetJsonAsync<T>(this FaReportingHttpClient client, string url, CancellationToken token = default)
    {
        var response = await client.GetAsync(url, token);
        await response.EnsureSuccessStatusCodeAsync();
        using var reader = new StreamReader(await response.Content.ReadAsStreamAsync());
        await using var jsonReader = new JsonTextReader(reader);
        var ser = new JsonSerializer();
        return ser.Deserialize<T>(jsonReader);
    }

    public static async Task<T> PostJsonAsync<T>(this FaReportingHttpClient client, string url, string json, CancellationToken token = default)
    {
        var buffer = Encoding.UTF8.GetBytes(json);
        var byteContent = new ByteArrayContent(buffer);
        byteContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
        var response = await client.PostAsync(url, byteContent, token);
        await response.EnsureSuccessStatusCodeAsync();
        using var reader = new StreamReader(await response.Content.ReadAsStreamAsync());
        await using var jsonReader = new JsonTextReader(reader);
        var ser = new JsonSerializer();
        return ser.Deserialize<T>(jsonReader);
    }
}
