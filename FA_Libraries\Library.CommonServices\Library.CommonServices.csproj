﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="StackExchange.Redis" Version="2.9.11" />
    <PackageReference Include="CassandraCSharpDriver" Version="3.22.0" />
    <PackageReference Include="Confluent.Kafka" Version="2.11.1" />
    <PackageReference Include="Microsoft.Azure.DocumentDB.Core" Version="2.22.0" />
    <PackageReference Include="Polly" Version="8.6.3" />
    <PackageReference Include="Sentry.AspNetCore" Version="5.14.1" />
    <PackageReference Include="Sentry.EntityFramework" Version="5.14.1" />
    <PackageReference Include="Sentry.Profiling" Version="5.14.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\fa_dotnet_core\FA.Cache\FA.Cache.csproj" />
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Library.MasterDb\Library.MasterDb.csproj" />
    <ProjectReference Include="..\Library.NumberSystem\Library.NumberSystem.csproj" />
    <ProjectReference Include="..\Library.ReverseGeoCoder\Library.ReverseGeoCoder.csproj" />
    <ProjectReference Include="..\Library.StringHelpers\Library.StringHelpers.csproj" />
  </ItemGroup>

</Project>
