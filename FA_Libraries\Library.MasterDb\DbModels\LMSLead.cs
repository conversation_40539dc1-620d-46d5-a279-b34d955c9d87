﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    [Table("LMSLeads")]
    public class LMSLead
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long CompanyId { get; set; }

        [Required]
        [ForeignKey("LMSAccount")]
        public long AccountId { get; set; }
        public virtual LMSAccount LMSAccount { get; set; }

        [Required]
        [ForeignKey("LMSCompanyLeadTemplate")]
        public long LeadTemplateId { get; set; }
        public virtual LMSCompanyLeadTemplate LMSCompanyLeadTemplate { get; set; }

        [Required]
        [ForeignKey("LMSCompanyLeadStage")]
        public long LeadStageId { get; set; }
        public virtual LMSCompanyLeadStage LMSCompanyLeadStage { get; set; }

        [Required]
        [StringLength(255)]
        public string LeadName { get; set; }

        [StringLength(500)]
        public string LeadImageUrl { get; set; }

        public string Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Amount { get; set; }

        [Required]
        [ForeignKey("LMSLeadSource")]
        public long LeadSourceId { get; set; }
        public virtual LMSLeadSource LMSLeadSource { get; set; }

        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; }

        [StringLength(255)]
        public string Website { get; set; }

        [StringLength(500)]
        public string Street { get; set; }
        [StringLength(2000)]
        public string City { get; set; }

        [StringLength(100)]
        public string State { get; set; }

        [StringLength(100)]
        public string Country { get; set; }
        [StringLength(200)]
        public string Pincode { get; set; }

        [Column(TypeName = "decimal(9, 6)")]
        public decimal? Latitude { get; set; }

        [Column(TypeName = "decimal(9, 6)")]
        public decimal? Longitude { get; set; }

        [Required]
        public LMSLeadStatus Status { get; set; }

        public LMSPriority? Priority { get; set; }

        public DateTime? ClosingDate { get; set; }

        public DateTime? FollowUpDate { get; set; }

        [Required]
        public long AssignedTo { get; set; }
        [Required]
        public long PositionCode { get; set; }

        [Required]
        public bool IsDeleted { get; set; } = false;

        [Required]
        public long CreatedBy { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
