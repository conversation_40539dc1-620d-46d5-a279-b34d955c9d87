using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using Core.Constants;
using Core.Loggers;
using HCCB.DbStorage.DbContexts;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace HCCBSchemeSnapshotProcessor
{
    public class SchemeSnapshotProcessor
    {
        private const int WaitingMinutesForReadingQueueItems = 5;
        private const string BothCompanyIdsString = "193054,193017";
        private readonly MasterDbSqlDataReader _masterDbSqlDataReader;
        private readonly QueueClient _schemeSnapShotQueueClient;
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly ILogger<SchemeSnapshotProcessor> _logger;

        public SchemeSnapshotProcessor(
            MasterDbSqlDataReader masterDbSqlDataReader,
            [FromKeyedServices(HCCBStorageQueues.SchemeSnapshotUpdateQueue)] QueueClient schemeSnapShotQueueClient,
            ISlackLogHelper slackLogHelper,
            ILogger<SchemeSnapshotProcessor> logger)
        {
            _masterDbSqlDataReader = masterDbSqlDataReader;
            _schemeSnapShotQueueClient = schemeSnapShotQueueClient;
            _slackLogHelper = slackLogHelper;
            _logger = logger;
        }

        [FunctionName("SchemeSnapshotProcessor")]
        public async Task ProcessSchemeSnapshotUpdate([QueueTrigger(HCCBStorageQueues.SchemeSnapshotUpdateQueue)] string queueMessage)
        {
            var startTime = DateTime.UtcNow;
            _logger.LogInformation($"SchemeSnapshotProcessor started at {startTime:yyyy-MM-dd HH:mm:ss} UTC");
            _logger.LogInformation($"Initial queue message received: {queueMessage}");

            try
            {
                while (true)
                {
                    // Step 1: Wait after receiving the initial item
                    _logger.LogInformation($"Waiting {WaitingMinutesForReadingQueueItems} minutes before reading additional queue messages...");
                    await Task.Delay(TimeSpan.FromMinutes(WaitingMinutesForReadingQueueItems));

                    // Step 2: Read all available items from the queue
                    var additionalMessages = await ReadAllAvailableMessages();

                    if (!additionalMessages.Any())
                    {
                        break;
                    }
                    // Step 3: Delete all additional messages from the queue
                    await DeleteProcessedMessages(additionalMessages);
                }

                // Step 4: Execute the SQL query
                await ExecuteSchemeSnapshotQuery();

                var endTime = DateTime.UtcNow;
                var duration = endTime - startTime;
                _logger.LogInformation($"SchemeSnapshotProcessor completed successfully at {endTime:yyyy-MM-dd HH:mm:ss} UTC. Duration: {duration.TotalMinutes:F2} minutes");
                await _slackLogHelper.SendCustomSlackMessage($"✅ **SchemeSnapshotProcessor Success**\n", isSuccessMessage: true,
                    $"Time: {DateTime.UtcNow.AddHours(5.5):yyyy-MM-dd HH:mm:ss} IST\n" +
                    $"SchemeSnapshotProcessor completed successfully at {endTime:yyyy-MM-dd HH:mm:ss} UTC. Duration: {duration.TotalMinutes:F2} minutes"
                    );
            }
            catch (Exception ex)
            {
                var errorMessage = $"SchemeSnapshotProcessor failed: {ex.Message}";
                _logger.LogError(ex, errorMessage);
                await _slackLogHelper.SendCustomSlackMessage($"❌ **SchemeSnapshotProcessor Error**\n", isSuccessMessage: false,
                    $"Time: {DateTime.UtcNow.AddHours(5.5):yyyy-MM-dd HH:mm:ss} IST\n" +
                    $"Error: {ex.Message}\n" +
                    $"Stack Trace: ```{ex.StackTrace}```");
                
                throw; // Re-throw to ensure the WebJob framework handles the failure appropriately
            }
        }

        private async Task<List<QueueMessage>> ReadAllAvailableMessages()
        {
            _logger.LogInformation("Reading all available messages from the queue...");
            
            // Read messages in batches (Azure Storage Queues supports up to 32 messages per call)
            bool hasMoreMessages = true;
            int totalMessagesRead = 0;
            var messages = new List<QueueMessage>();
            while (hasMoreMessages)
            {
                var response = await _schemeSnapShotQueueClient.ReceiveMessagesAsync(maxMessages: 32, visibilityTimeout: TimeSpan.FromMinutes(30));
                    
                if (response.Value != null && response.Value.Length > 0)
                {
                    messages.AddRange(response.Value);
                    totalMessagesRead += response.Value.Length;
                    _logger.LogInformation($"Read {response.Value.Length} messages from queue (Total: {totalMessagesRead})");
                }
                else
                {
                    hasMoreMessages = false;
                    _logger.LogInformation("No more messages available in the queue");
                }
            }

            _logger.LogInformation($"Total additional messages read from queue: {totalMessagesRead}");
            return messages;
        }

        private async Task DeleteProcessedMessages(List<QueueMessage> messages)
        {
            if (messages.Count == 0)
            {
                _logger.LogInformation("No additional messages to delete");
                return;
            }

            _logger.LogInformation($"Deleting {messages.Count} processed messages from queue...");
            
            try
            {
                var deleteTasks = messages.Select(async message =>
                {
                    try
                    {
                        await _schemeSnapShotQueueClient.DeleteMessageAsync(message.MessageId, message.PopReceipt);
                        _logger.LogDebug($"Deleted message: {message.MessageId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"Failed to delete message {message.MessageId}: {ex.Message}");
                    }
                });

                await Task.WhenAll(deleteTasks);
                _logger.LogInformation($"Completed deletion of {messages.Count} messages");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting messages from queue");
            }
        }

        private async Task ExecuteSchemeSnapshotQuery()
        {
            _logger.LogInformation("Executing scheme snapshot SQL query...");
            
            var sqlQuery = $@"
                DECLARE @ISTNow DATETIME2 = DATEADD(MINUTE, 330, GETUTCDATE());

                BEGIN TRAN T1
                TRUNCATE TABLE dbo.HCCBSchemeSnapshot;
                INSERT INTO dbo.HCCBSchemeSnapshot (Id, CompanyId, DistributorId, OutletConstraints)
                SELECT 
                    s.Id,
                    s.CompanyId, 
                    ISNULL(TRY_CAST(s.DistributorIdBlock AS bigint), 0) as DistributorId,
                    s.OutletConstraints
                FROM dbo.Schemes AS s
                WHERE Id in (
                    SELECT [s].Id
                    FROM [Schemes] AS [s]
                    WHERE [s].[CompanyId] in ({BothCompanyIdsString})
                    AND [s].[IsDeleted] = 0
                    AND [s].[IsActive] = 1
                    AND [s].[IsQPS] = 0 
                    AND [s].[IsTwoQualifierScheme] = 0
                    AND [s].[StartTime] <= @ISTNow
                    AND [s].[EndTime] >= @ISTNow
                );
                COMMIT TRAN T1";

            var result = await _masterDbSqlDataReader.RunQuery(sqlQuery, commandTimeout: 600);
            _logger.LogInformation($"Scheme snapshot query executed successfully. Rows affected: {result}");
        }
    }
}
