﻿// Copyright (c) FieldAssist. All Rights Reserved.
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("LMSAccounts")]
    public class LMSAccount
    {
        [Key]
        public long Id { get; set; }
        [ForeignKey("Company")]
        public long CompanyId { get; set; }

        public long AssignedTo { get; set; }
        [ForeignKey("PositionCodes")]
        public long PositionCode { get; set; }

        [StringLength(255)]
        public string AccountName { get; set; }

        public string Description { get; set; }

        [StringLength(255)]
        public string Email { get; set; }

        [StringLength(255)]
        public string Website { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? AnnualRevenue { get; set; }

        public string AccountImage { get; set; }

        [ForeignKey("LMSAccountTemplate")]
        public long AccountTemplateId { get; set; }
        public virtual LMSAccountTemplate LMSAccountTemplate { get; set; }

        public bool IsActive { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; }

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual Company Company { get; set; }
        public virtual PositionCode PositionCodes { get; set; }

        public virtual ICollection<LMSAccountContact> LMSAccountContacts { get; set; }
        public virtual ICollection<LMSAccountAddress> LMSAccountAddresses { get; set; }
        public virtual ICollection<LMSAccountNote> LMSAccountNotes { get; set; }
        public virtual ICollection<LMSLead> LMSLeads { get; set; }

        public LMSAccount()
        {
            LMSAccountContacts = new HashSet<LMSAccountContact>();
            LMSAccountAddresses = new HashSet<LMSAccountAddress>();
            LMSAccountNotes = new HashSet<LMSAccountNote>();
            LMSLeads = new HashSet<LMSLead>();
        }
    }
}
