﻿using Core.Models.MasterDbModels;
using Libraries.CommonEnums;

namespace Core.Repositories
{
    public interface IReuestTimelineRepository
    {
        Task<List<RequestApprovalTimeline>> GetRequestTimelines(long companyId, long requestId, ApprovalEngineRequestType requestType);
        Task<RequestApprovalTimeline> GetRequestTimelineForStatus(long companyId, long requestId, ApprovalEngineRequestType requestType, ApprovalEngineRequestStatus status);
        Task AddRequestToTimeline(RequestApprovalTimeline request);
        Task UpdateAndApproveRequest(RequestApprovalTimeline request, string status);
    }
}
