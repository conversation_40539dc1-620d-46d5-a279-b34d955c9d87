﻿using Core.Models;
using HCCB.Tests.Configurations;
using CTSStatusProcessor.Configuration;
using CTSStatusProcessor;
using Libraries.CommonEnums;
using Microsoft.Extensions.DependencyInjection;

namespace HCCB.Tests.CtsQueueProcessor
{
    [TestClass]
    public class CtsStatusProcessorTest
    {
        private ServiceProvider serviceProvider;

        [TestInitialize]
        public void Initialise()
        {
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageWritable.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, configuration);
            serviceCollection.AddScoped<CTSQueueProcessor>();
            serviceProvider = serviceCollection.BuildServiceProvider();
        }


        [TestMethod]
        public void CtsStatusQueueProcessor()
        {
            var qp = serviceProvider.GetRequiredService<CTSQueueProcessor>();
            var data = new QueueData()
            {
                CompanyId = 193017,
                RequestId = 22963,
                ApprovalEngineRequestType = ApprovalEngineRequestType.AssetReallocation
            };
            // {"CompanyId":193017,"RequestId":160636,"RequestType":3,"RequestSubType":null}

            qp.CtsStatusQueueProcessor(data).Wait();
        }
    }
}
