﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Telegram.Bot" Version="19.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\Core.csproj" />
    <ProjectReference Include="..\FA_Libraries\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.ResponseHelpers\Library.ResponseHelpers.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>

</Project>
