﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <RootNamespace>CueCard_ADLS_Data</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Files.DataLake" Version="12.17.1" />
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.39" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.Storage" Version="5.2.2" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.0-Preview.2" />
    <PackageReference Include="Parquet.Net" Version="4.23.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Core\Core.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.CommonHelpers\Library.CommonHelpers.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.ResponseHelpers\Library.ResponseHelpers.csproj" />
    <ProjectReference Include="..\HCCB-ADLS.DbStorage\HCCB_ADLS.DbStorage.csproj" />
    <ProjectReference Include="..\HCCB.DbStorage\HCCB.DbStorage.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
