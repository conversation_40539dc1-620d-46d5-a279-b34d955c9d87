﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Net.NetworkInformation;

namespace Library.NetworkingHelper;

public static class NetworkingHelper
{
    public static string GetMacAddress()
    {
        foreach (var nic in NetworkInterface.GetAllNetworkInterfaces())
        {
            if (nic.OperationalStatus == OperationalStatus.Up)
            {
                var address = nic.GetPhysicalAddress();
                var bytes = address.GetAddressBytes();
                var macAddress = BitConverter.ToString(bytes).Replace("-", ":");
                return macAddress;
            }
        }

        return null;
    }
}

