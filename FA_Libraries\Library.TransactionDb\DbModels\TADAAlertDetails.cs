﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace Library.TransactionDb.DbModels;

public class TADAAlertDetails
{
    public long Id { get; set; }

    public long ESMId { get; set; }

    public long ExpenseDate { get; set; }

    public string OutstationType { get; set; }

    public Guid ApprovalId { get; set; }

    public Guid ActivityId { get; set; }

    public string CityGrade { get; set; }

    public string TA_Type { get; set; }

    public string EditableFields { get; set; }

    public long CompanyId { get; set; }

    public DateTime CreatedAt { get; set; }

    // KM Based TA
    public string TravelFrom { get; set; }

    public string TravelTo { get; set; }

    public double KMTravel { get; set; }

    public string VehicleType { get; set; }

    public string RemarksTA1 { get; set; }

    public double TAAmountKMWise { get; set; }

    // Actual Based TA
    public double TAClaimAmount { get; set; }

    public string TravelPurpose { get; set; }

    public string ActualBasisTravelFrom { get; set; }

    public string ActualBasisTravelTo { get; set; }

    public string BillImage { get; set; }

    public string RemarksTA2 { get; set; }

    // ClaimBased TA
    public double NightTravel { get; set; }

    public string NightTravelBillURL { get; set; }

    public double NightStay { get; set; }

    public string NightStayBillURL { get; set; }

    public double Lodging { get; set; }

    public string LodgingBillURL { get; set; }

    public double Boarding { get; set; }

    public string BoardingBillURL { get; set; }

    public double Marketing { get; set; }

    public string MarketingBillURL { get; set; }

    public double Stationery { get; set; }

    public string StationeryBillURL { get; set; }

    public double Food { get; set; }

    public string FoodBillURL { get; set; }

    public double Entertainment { get; set; }

    public string EntertainmentBillURL { get; set; }

    public double Mobile { get; set; }

    public string MobileBillURL { get; set; }

    public double Courier { get; set; }

    public string CourierBillURL { get; set; }

    public double Conveyance { get; set; }

    public string ConveyanceBillURL { get; set; }

    public double Others { get; set; }

    public string OtherBillURL { get; set; }

    public string WorkflowId { get; set; }

    public bool isReviewed { get; set; }

    public long? ActionTakenBy { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public Status Status { get; set; }

    public string RejectionRemarks { get; set; }

    public bool IsEdited { get; set; }

    public long? ApprovalPendingAt { get; set; }

    public double OtherExpenseSum => NightTravel + NightStay + Lodging + Boarding + Conveyance + Marketing +
                                     Stationery + Courier + Food + Entertainment + Mobile + Others + Flight +
                                     LodgingWithoutStay;

    public string LodgingWithoutStayBillURL { get; set; }

    public string FlightBillURL { get; set; }

    public double Flight { get; set; }

    public string DayEndMeterImage { get; set; }

    public string DayStartMeterImage { get; set; }

    public long TADAGSTDurationDetailsId { get; set; }

    public double LodgingWithoutStay { get; set; }

    public string FixedBasisTA { get; set; }

    public string OtherExpense { get; set; }

    public string AutomatedExpense { get; set; }
    public string FlightBillUrl { get; set; }
    public double VehicleCoefficient { get; set; }

    public FixedBasis GetFixedBasis()
    {
        if (string.IsNullOrEmpty(FixedBasisTA))
        {
            return new FixedBasis();
        }

        var data = JsonConvert.DeserializeObject<FixedBasis>(FixedBasisTA);
        return data ?? new FixedBasis();
    }

    public List<ExpenseDetailsBillURL> GetOtherExpense()
    {
        if (string.IsNullOrEmpty(OtherExpense))
        {
            return new List<ExpenseDetailsBillURL>();
        }

        var data = JsonConvert.DeserializeObject<List<ExpenseDetailsBillURL>>(OtherExpense);
        return data ?? new List<ExpenseDetailsBillURL>();
    }

    public List<ExpenseDetails> GetAutomatedExpense()
    {
        if (string.IsNullOrEmpty(AutomatedExpense))
        {
            return new List<ExpenseDetails>();
        }

        var data = JsonConvert.DeserializeObject<List<ExpenseDetails>>(AutomatedExpense);
        return data ?? new List<ExpenseDetails>();
    }

    public double TotalAmount =>
        (string.IsNullOrEmpty(OtherExpense) ? OtherExpenseSum : GetOtherExpense().Sum(x => x.Amount)) +
        TAAmountKMWise + TAClaimAmount + GetFixedBasis().Amount +
        GetAutomatedExpense().Sum(x => x.Amount);
}
