﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Library.Infrastructure\Library.Infrastructure.csproj" />
    <ProjectReference Include="..\Library.ResilientHttpClient\Library.ResilientHttpClient.csproj" />
    <ProjectReference Include="..\Library.SlackService\Library.SlackService.csproj" />
  </ItemGroup>

</Project>
