﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Flurl.Http.Configuration;
using Library.ResilientHttpClient;

namespace Library.ReverseGeoCoder
{
    public class GoogleMapsService
    {
        private readonly ResilientAPIActions _client;
        private readonly string baseUrl;

        public GoogleMapsService(IFlurlClientCache flurlClientCache, string baseUrl)
        {
            _client = new ResilientAPIActions(flurlClientCache, baseUrl, "");
            this.baseUrl = baseUrl;
        }

        //TODO: Get Distance of Multiple Points Simultaneously https://developers.google.com/maps/documentation/distance-matrix/distance-matrix#DistanceMatrixStatus
        public async Task<double> GetDistanceBetweenTwoLocations(decimal originLatitude = 0, decimal originLongitude = 0, decimal destinationLatitude = 0, decimal destinationLongitude = 0, string apiKey = "")
        {
            var api = $"distanceMatrix/v2:computeRouteMatrix";

            var requestBody = new
            {
                origins = new[]
                {
                    new { waypoint = new { location = new { latLng = new { latitude = originLatitude, longitude = originLongitude } } } }
                },

                destinations = new[]
                {
                    new { waypoint = new { location = new { latLng = new { latitude = destinationLatitude, longitude = destinationLongitude } } } }
                },
            };

            var headers = new Dictionary<string, string>
            {
                { "X-Goog-Api-Key", apiKey },
                { "X-Goog-FieldMask", "originIndex,destinationIndex,duration,distanceMeters,status" },
                { "Content-Type", "application/json" }
            };

            var rawResponse = await _client.PostJsonAsync<object>(api, requestBody, headers);

            if (rawResponse == null)
            {
                return 0.0;
            }

            var errorToken = (rawResponse as Newtonsoft.Json.Linq.JObject)?["error"];
            if (errorToken != null)
            {
                var code = (int?)errorToken["code"];
                var status = (string)errorToken["status"];
                if (code == 403 && status == "PERMISSION_DENIED")
                {
                    return 0.0;
                }

                throw new Exception($"DistanceMatrix API error {code}: {status}");
            }

            var response = (rawResponse as Newtonsoft.Json.Linq.JArray)?.ToObject<List<RouteMatrixElement>>();
            if (response != null && response.Any() && response[0].Status == "OK")
            {
                return Convert.ToDouble(response[0].DistanceMeters);
            }

            return 0.0;
        }
    }
}
