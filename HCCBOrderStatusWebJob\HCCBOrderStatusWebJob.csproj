<Project Sdk="Microsoft.NET.Sdk;Microsoft.NET.Sdk.Publish">
	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.39" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.4" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
		<PackageReference Include="Telegram.Bot" Version="19.0.0" />
		<PackageReference Include="Serilog" Version="3.1.1" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\FA_Libraries\Library.CommonHelpers\Library.CommonHelpers.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="appsettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="localhost.cer">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>