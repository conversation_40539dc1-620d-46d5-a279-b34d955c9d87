using HCCBCtsApprovalProcessor.Models;
using Library.CommonHelpers.Utils;
using Library.CommonHelpers;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using Core.Loggers;
using Libraries.CommonEnums;
using Core.Models;
using Core.Models.CTS;
using Polly;
using System.Text.Json;
using System.Net.Http.Headers;
using Core.Models.MasterDbModels;
using Core.Repositories;
using Libraries.CommonModels;

namespace HCCBCtsApprovalProcessor.Services
{
    public class CtsService
    {
        private readonly HttpClient hccbHttpClient;
        private readonly ITransactionRepository transactionRepository;
        private readonly IFaMasterRepository faMasterRepository;
        private readonly AppConfigSettings appConfigSettings;
        private readonly IHttpClientFactory httpClientFactory;
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly ImageTransformationService imageTransformationService;
        private readonly IReuestTimelineRepository reuestTimelineRepository;
        private readonly CtsHccbApiValues hccbApiValues;
        private readonly FileLogger fileLogger;

        private const long UATCompanyId = 193017;
        private const long ProdCompanyId = 193054;

        public CtsService(HttpClient hccbHttpClient, ITransactionRepository transactionRepository, IFaMasterRepository faMasterRepository,
           AppConfigSettings appConfigSettings, IHttpClientFactory httpClientFactory,
           ISlackLogHelper slackLogHelper, CtsHccbApiValues hccbApiValues, FileLogger fileLogger,
           ImageTransformationService imageTransformationService, IReuestTimelineRepository reuestTimelineRepository)
        {
            this.hccbHttpClient = hccbHttpClient;
            this.transactionRepository = transactionRepository;
            this.faMasterRepository = faMasterRepository;
            this.appConfigSettings = appConfigSettings;
            this.httpClientFactory = httpClientFactory;
            _slackLogHelper = slackLogHelper;
            this.hccbApiValues = hccbApiValues;
            this.fileLogger = fileLogger;
            this.imageTransformationService = imageTransformationService;
            this.reuestTimelineRepository = reuestTimelineRepository;
        }

        public async Task ProcessRequest(QueueData queueData)
        {
            await CallCtsApi(queueData);
            var existingTimeline = await reuestTimelineRepository.GetRequestTimelines(queueData.CompanyId, queueData.RequestId, queueData.ApprovalEngineRequestType);
            if (existingTimeline == null)
            {
                await _slackLogHelper.SendMessageToSlack($"Request Timeline data not found for request {queueData.RequestId}");
                throw new Exception($"Request Timeline data not found for request {queueData.RequestId}");
            }
            if (!existingTimeline.Any(t => t.RequestStatus == ApprovalEngineRequestStatus.ExternalPending))
            {
                var timeline = existingTimeline.Where(t => t.Sequence==1).FirstOrDefault();
                if(timeline == null)
                {
                    await _slackLogHelper.SendMessageToSlack($"Request Timeline data not found for request {queueData.RequestId}");
                    throw new Exception($"Request Timeline data not found for request {queueData.RequestId}");
                }
                await AddRequestToTimeline(queueData, timeline);
            }
            return;
        }
        public async Task AddRequestToTimeline(QueueData queueData, RequestApprovalTimeline requestApprovalTimeline)
        {
            try
            {
                var positionId = 0;
                var userId = 0;
                if (queueData.CompanyId == UATCompanyId) { positionId = 1128285; userId = 13244461; };
                if (queueData.CompanyId == ProdCompanyId) { positionId = 1140721; userId = 13245470; };
                var request = new RequestApprovalTimeline
                {
                    RequestId = queueData.RequestId,
                    RequestType = queueData.ApprovalEngineRequestType,
                    Sequence = requestApprovalTimeline.Sequence + 1,
                    ApproverPositionId = positionId,
                    ApproverPositionLevel = PositionCodeLevel.L3Position,
                    RequesterPositionId = requestApprovalTimeline.RequesterPositionId,
                    RequesterUserId = requestApprovalTimeline.RequesterUserId,
                    RequesterPositionLevel = requestApprovalTimeline.RequesterPositionLevel,
                    RequestStatus = ApprovalEngineRequestStatus.ExternalPending,
                    RuleId = requestApprovalTimeline.RuleId,
                    ApproverUserId = userId == 0 ? null : userId,
                    CompanyId = queueData.CompanyId,
                    CreatedAt = DateTime.UtcNow,
                    LastUpdatedAt = DateTime.UtcNow,
                    CreationContext = "CtsApprovalProcessor",
                    RequestStatusString = "CDE Status pending"
                };
                await reuestTimelineRepository.AddRequestToTimeline(request);
            }
            catch (Exception ex)
            {
                await _slackLogHelper.SendMessageToSlack($"Error adding request to timeline for request {queueData.RequestId}: {ex.Message}");
                throw;
            }
        }
        private async Task CallCtsApi(QueueData queueData)
        {
            try
            {
                var requestData = await GetRequestData(queueData);
                if (requestData == null)
                {
                    await _slackLogHelper.SendMessageToSlack($"Request not found in db for requestId: {queueData.RequestId}");
                    throw new Exception($"Request not found in db for requestId: {queueData.RequestId}");
                }

                var surveyData = await GetSurveyData(queueData, requestData?.FAEventId);
                if (surveyData == null)
                {
                    await _slackLogHelper.SendMessageToSlack($"Survey not found for request {queueData.RequestId}");
                    throw new Exception($"Survey not found for request {queueData.RequestId}");
                }

                switch (queueData.ApprovalEngineRequestType)
                {
                    case ApprovalEngineRequestType.AssetAllocation:
                        var ctsData = await TransormaDataForPlacement(surveyData, queueData, requestData?.LocationId, requestData?.ApprovedBy);
                        await PostApprovedAssetAllocationToCTS(ctsData, queueData.CompanyId);
                        break;
                    case ApprovalEngineRequestType.AssetReallocation:
                        var outletList = new List<long> { requestData?.CurrentOutletId };
                        if (requestData?.NewOutletId != null)
                        {
                            outletList.Add(requestData?.NewOutletId);
                        }
                        var outletDetails = await faMasterRepository.GetOutletsByIds(outletList);
                        var RequestedById = await faMasterRepository.GetEmployeeErpById(requestData?.RequestedById);
                        var ReviewedById = await faMasterRepository.GetEmployeeErpById(requestData?.ReviewedById);
                        var assetErpId = await faMasterRepository.GetAssetErpIdByAssetMappingId(requestData?.AssetOutletMappingsId, queueData.CompanyId);

                        switch (queueData.AssetReallocationType)
                        {
                            case AssetReallocationType.Reallocation:
                                var transferData = new CtsTransferModel(surveyData, queueData, outletDetails, RequestedById, ReviewedById, 
                                    assetErpId, requestData);
                                await PostTransferRequestToCTS(transferData, queueData.CompanyId);
                                break;
                            case AssetReallocationType.Breakdown:
                                var breakdownData = new CtsBreakdownModel(surveyData, queueData, outletDetails, RequestedById, ReviewedById, 
                                    assetErpId, requestData);
                                await PostBreakdownRequestToCTS(breakdownData, queueData.CompanyId);
                                break;
                            case AssetReallocationType.Removal:
                                var removalData = new CtsRemovalModel(surveyData, queueData, outletDetails, RequestedById, ReviewedById, 
                                    assetErpId, requestData);
                                await PostRemovalRequestToCTS(removalData, queueData.CompanyId);
                                break;
                        }
                        break;
                    default:
                        await _slackLogHelper.SendMessageToSlack($"Unsupported request type {queueData.ApprovalEngineRequestType} for request {queueData.RequestId}");
                        break;
                }
            }
            catch (Exception ex)
            {
                await _slackLogHelper.SendMessageToSlack($"Error processing request {queueData.RequestId}: {ex.Message}");
                throw;
            }
        }

        private async Task<dynamic?> GetRequestData(QueueData queueData)
        {
            return queueData.ApprovalEngineRequestType switch
            {
                ApprovalEngineRequestType.AssetAllocation => await transactionRepository.GetAssetAllocationRequestById(queueData.RequestId, queueData.CompanyId),
                ApprovalEngineRequestType.AssetReallocation => await transactionRepository.GetAssetRellocationRequestById(queueData.RequestId, queueData.CompanyId),
                _ => throw new NotImplementedException()
            };
        }
        private async Task<SurveyResponse> GetSurveyData(QueueData queueData, long eventId)
        {
            var api = $"api/Survey/surveyForEventId?companyId={queueData.CompanyId}&eventId={eventId}";

            string apiBasePath = appConfigSettings.reportApiBaseUrl;
            string apiUrl = $"{apiBasePath}{api}";
            string authorizationHeader = $"Bearer {appConfigSettings.reportApiToken}";
            using var httpClient = httpClientFactory.CreateClient();

            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            httpClient.Timeout = TimeSpan.FromMinutes(5);

            var orderIds = new List<long>();
            try
            {
                HttpResponseMessage response = await httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<SurveyResponse>(responseContent);
            }
            catch (HttpRequestException ex)
            {
                await _slackLogHelper.SendMessageToSlack($"Failed to fetch survey data for request {queueData.RequestId} with error: {ex.Message}");
                throw;
            }
        }

        public async Task<StreamContent> PrepareImageZipStreamAsync<T>(T model) where T : class
        {
            var zipStream = new MemoryStream();
            await imageTransformationService.TransformImagesAsync(model, zipStream);

            var streamContent = new StreamContent(zipStream);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue("application/zip");

            return streamContent;
        }
        private async Task<CtsPlacementModel> TransormaDataForPlacement(SurveyResponse survey, QueueData queueData, long locationId, long managerId)
        {
            var managerErpId = await faMasterRepository.GetEmployeeErpById(managerId);
            var outletDetails = faMasterRepository.GetOutletsByIds(new List<long> { locationId }).Result.FirstOrDefault();
            var assestName = survey.Questions.Where(a => a.QuestionTitle == "Request for CDE Model").Select(b => b.Answer).FirstOrDefault();
            var assetErpId = await faMasterRepository.GetAssetDefinitionErpIdByName(assestName, queueData.CompanyId);
            var outletErpId = outletDetails != null ? outletDetails.ErpId : "";
            return new CtsPlacementModel(survey, queueData, outletErpId, managerErpId, assetErpId);
        }
        private async Task PostApprovedAssetAllocationToCTS(CtsPlacementModel ctsData, long companyId)
        {
            var token = await GetAuthToken(companyId);

            if (string.IsNullOrWhiteSpace(ctsData.UploadDocument1) && string.IsNullOrWhiteSpace(ctsData.UploadDocument2) && string.IsNullOrWhiteSpace(ctsData.UploadDocument3))
            {
                throw new ArgumentException("Survey Data had no file to upload");
            }
            using (var streamContent = await PrepareImageZipStreamAsync(ctsData))
            {
                var content = ctsData.CreateMultipartFormDataContent(streamContent);

                var url = hccbApiValues.ctsPlacementUrl;
                if (companyId == UATCompanyId)
                {
                    url = hccbApiValues.ctsPlacementUrl_UAT;
                }

                await GetApiResponse(url, token, content);
           }
        }

        public async Task PostTransferRequestToCTS(CtsTransferModel ctsData, long companyId)
        {
            var token = await GetAuthToken(companyId);

            using (var streamContent = await PrepareImageZipStreamAsync(ctsData))
            {
                var content = ctsData.CreateMultipartFormDataContent(streamContent);
                var url = companyId == UATCompanyId ? hccbApiValues.ctsTransferUrl_UAT : hccbApiValues.ctsTransferUrl;
                await GetApiResponse(url, token, content);
            }
        }

        public async Task PostBreakdownRequestToCTS(CtsBreakdownModel breakdownData, long companyId)
        {
            var token = await GetAuthToken(companyId);

            var content = breakdownData.CreateMultipartFormDataContent();
            var url = companyId == UATCompanyId ? hccbApiValues.ctsBreakdownUrl_UAT : hccbApiValues.ctsBreakdownUrl;
            await GetApiResponse(url, token, content);
        }

        public async Task PostRemovalRequestToCTS(CtsRemovalModel removalData, long companyId)
        {
            var token = await GetAuthToken(companyId);

            var content = removalData.CreateMultipartFormDataContent();
            var url = companyId == UATCompanyId ? hccbApiValues.ctsRemovalUrl_UAT : hccbApiValues.ctsRemovalUrl;
            await GetApiResponse(url, token, content);
        }

        private async Task<string> GetAuthToken(long companyId)
        {
            string token;
            string url;
            Dictionary<string, string> postData;
            if (companyId == UATCompanyId)
            {
                postData = new Dictionary<string, string>
                {
                    { "grant_type", "client_credentials" },
                    { "client_id", hccbApiValues.clientId_UAT },
                    { "client_secret", hccbApiValues.clientSecret_UAT }
                };
                token = hccbApiValues.accessToken_UAT;
                url = hccbApiValues.tokenUrl_UAT;
            }
            else
            {
                postData = new Dictionary<string, string>
                {
                    { "grant_type", "client_credentials" },
                    { "client_id", hccbApiValues.clientId },
                    { "client_secret", hccbApiValues.clientSecret }
                };
                token = hccbApiValues.accessToken;
                url = hccbApiValues.tokenUrl;
            }

            var content = new FormUrlEncodedContent(postData);

            hccbHttpClient.DefaultRequestHeaders.Clear(); // Clear headers to ensure no leftover headers
            hccbHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await hccbHttpClient.PostAsync(url, content);
            await response.EnsureSuccessStatusCodeAsync();
            var jsonResponse = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<OAuthResponse>(jsonResponse).AccessToken;
        }
        private async Task GetApiResponse(string url, string token, MultipartFormDataContent content)
        {
            hccbHttpClient.DefaultRequestHeaders.Clear();
            hccbHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            var response = await Policy
                .Handle<HttpRequestException>()
                .OrResult<HttpResponseMessage>(r =>
                {
                    var responseString = r.Content.ReadAsStringAsync().Result;
                    var errorResponse = JsonSerializer.Deserialize<HccbErrorResponse>(responseString);
                    return !r.IsSuccessStatusCode && (errorResponse?.error?.detail?.code != "404");
                })
                .RetryAsync(3, async (exception, retryCount) =>
                {
                    await _slackLogHelper.SendMessageToSlack($"Retry {retryCount} for posting to CTS due to error: {exception.Exception?.Message ?? exception.Result.ReasonPhrase}");
                })
                .ExecuteAsync(() => hccbHttpClient.PostAsync(url, content));

            var responseString = await response.Content.ReadAsStringAsync();
            var errorResponse = JsonSerializer.Deserialize<HccbErrorResponse>(responseString);
            await _slackLogHelper.SendMessageToSlack(ConstructSlackMessage(url, response, await ExtractFormData(content), responseString));

            if (!response.IsSuccessStatusCode && errorResponse?.error?.detail?.code != "404")
            {
                throw new Exception($"Error posting to CTS: ```{responseString}```");
            }
            return;
        }
        private string ConstructSlackMessage(string apiName, HttpResponseMessage response, string request, string responseBody)
        {
            var timeStamp = DateTime.UtcNow.AddHours(5.5).ToString("dd/MM/yyyy hh:mm:ss tt");
            var status = response.IsSuccessStatusCode ? "✅ Success" : "❌ Failure";

            return $"> {status}: {apiName} API - <Timestamp [IST]: {timeStamp}>\n" +
                   $"*Status Code:* {(int)response.StatusCode}\n" +
                   $"*Endpoint:* {response.RequestMessage?.RequestUri}\n" +
                   $"*Request:* ```{request}```\n" +
                   $"*Response:* ```{responseBody}```";
        }

        private async Task<string> ExtractFormData(MultipartFormDataContent content)
        {
            var keyValues = await Task.WhenAll(content
                .Where(x => x.Headers.ContentDisposition?.Name?.Trim('"') != "Upload_Document")
                .Select(async x => $"{x.Headers.ContentDisposition?.Name?.Trim('"')}={await x.ReadAsStringAsync()}"));

            return string.Join(", ", keyValues);
        }
    }
}
