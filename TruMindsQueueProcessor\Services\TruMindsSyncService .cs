using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Core.Models.HccbDbModels;
using Library.CommonHelpers;
using HCCB.DbStorage.Repositories.MasterRepositories;
using Library.StorageWriter.Reader_Writer;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using TruMindsQueueProcessor.Models;
using TruMindsQueueProcessor.Models.ApiModels;
using System.Globalization;
using System.Net;
using Library.SlackService.Interfaces;

namespace TruMindsQueueProcessor.Services
{
    public class TruMindsSyncService
    {
        private readonly HttpClient _httpClient;
        private readonly ISlackLogger _slackLogger;
        private readonly HCCBBlobReader _blobReader;
        private readonly IFaMasterRepository _faMasterRepository;
        private readonly TruMindsConfig _options;

        public TruMindsSyncService(HttpClient httpClient, ISlackLogger slackLogger, HCCBBlobReader hCCBBlobReader, IFaMasterRepository faMasterRepository, IOptions<TruMindsConfig> options)
        {
            _httpClient = httpClient;
            _slackLogger = slackLogger;
            _blobReader = hCCBBlobReader;
            _faMasterRepository = faMasterRepository;
            _options = options.Value;
        }

        public async Task<bool> SendToTruMindsFormDataAsync(MaxerienceLog log, List<Scenes> scenes)
        {
            if (scenes.All(s => string.IsNullOrWhiteSpace(s.ImageBlobPath)))
            {
                Console.WriteLine($"No Images found in the log: {log.Id}");
                return true; // No images to send, consider it a success
            }

            // Prepare image data
            var employeeErpId = await _faMasterRepository.GetEmployeeErpById(log.UserId);
            var outletErpId = (await _faMasterRepository.GetOutletsByIds([log.OutletId]))?.FirstOrDefault()?.ErpId ?? "0000000000";

            var sessionData = new TruMindsRootObject
            {
                Session = new List<Session>
                {
                    new Session
                    {
                        SessionUid = log.MaxerienceSessionId,
                        SessionStartTime = ((DateTimeOffset)log.MaxerienceSessionStartTime).ToUnixTimeMilliseconds().ToString(),
                        UserReferenceId = employeeErpId,
                        OutletCode = outletErpId,
                        VisitDate = log.MaxerienceSessionStartTime.ToString("yyyy-MM-dd"),
                        LocalTimeZone = "Asia/Kolkata",
                        Latitude = (double)(log.Latitude ?? 0),
                        Longitude = (double)(log.Longitude ?? 0),
                        SurveyStatus = 0,
                        Scene = scenes
                            .GroupBy(scene => new { scene.SceneTypeCode, scene.SubSceneTypeCode })
                            .Select(group => new Scene
                            {
                                SceneUid = Guid.NewGuid().ToString(),
                                AssetCode = group.FirstOrDefault()?.AssetId,
                                SceneCaptureTime = ((DateTimeOffset)group.Min(x => x.CaptureTime)).ToUnixTimeMilliseconds().ToString(),
                                SceneType = group.Key.SceneTypeCode,
                                SceneSubType = group.Key.SubSceneTypeCode,
                                ImageCount = group.Count(x => !string.IsNullOrEmpty(x.ImageBlobPath)),
                                Images = group
                                    .Where(scene => !string.IsNullOrEmpty(scene.ImageBlobPath))
                                    .Select(scene => new Image
                                    {
                                        ImageUid = Path.GetFileName(scene.ImageBlobPath),
                                        ImageCaptureTime = ((DateTimeOffset)scene.CaptureTime).ToUnixTimeMilliseconds().ToString(),
                                        ImageSerialNumber = int.TryParse(scene.ImageSerialNumber, out var serial) ? serial : 0,
                                        ImageGroupId = scene.ImageGroupId ?? 1,
                                        ImageAttributes = new ImageAttributes(),
                                        ImageFileName = Path.GetFileName(scene.ImageBlobPath)
                                    })
                                    .ToList()
                            })
                            .ToList()
                    }
                }
            };

            using var formData = new MultipartFormDataContent();

            // Add JSON payload
            var json = JsonConvert.SerializeObject(sessionData);
            formData.Add(new StringContent(json, Encoding.UTF8, "text/plain"), "data");

            // Add blob image file
            int fileIndex = 1;
            foreach (var scene in scenes)
            {
                if (string.IsNullOrWhiteSpace(scene.ImageBlobPath))
                    continue;

                string filePath = scene.ImageBlobPath;
                string fileName = Path.GetFileName(filePath);

                using var blobStream = await _blobReader.ReadDatBlobContentAsync(filePath);
                var memoryStream = new MemoryStream();
                await blobStream.CopyToAsync(memoryStream);
                memoryStream.Position = 0;

                var streamContent = new StreamContent(memoryStream);
                streamContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");

                formData.Add(streamContent, $"file_field_{fileIndex++}", fileName);
            }

            var endpoint = new Uri(new Uri(_options.ApiBaseUrl), _options.UploadEndpoint).ToString();
            using var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
            {
                Content = formData
            };

            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await GetTruMindsJwtTokenAsync());

            using var response = await _httpClient.SendAsync(request);
            await response.EnsureSuccessStatusCodeAsync(logRequestContent: true);

            // Log success
            await LogTruMindsSuccess(response, endpoint, json);
            return true;
        }

        private async Task<string?> GetTruMindsJwtTokenAsync()
        {
            var loginUrl = new Uri(new Uri(_options.ApiBaseUrl), _options.LoginEndpoint);

            var credentials = new
            {
                username = _options.Username,
                password = _options.Password
            };

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(credentials), Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(loginUrl, content);

            await response.EnsureSuccessStatusCodeAsync(logRequestContent: true);

            var json = await response.Content.ReadAsStringAsync();
            var tokenObj = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(json);
            return tokenObj.GetProperty("access_token").GetString();
        }

        private async Task LogTruMindsSuccess(HttpResponseMessage response, string endpoint, string requestJson)
        {
            string timeStamp = GetCurrentISTTimestamp();
            string responseContent = await response.Content.ReadAsStringAsync();

            string message = $"✅ Success: TruMinds API - <Timestamp [IST]: {timeStamp}>\n" +
                             $"Success Message:\n\n" +
                             $"Success: HTTP Status: {(int)response.StatusCode} - {response.ReasonPhrase}\n" +
                             $"Response: {responseContent}\n\n" +
                             $"Endpoint: {endpoint}\n" +
                             $"Body:\n```\n{requestJson}\n```";

            _slackLogger.LogInfo(message);
        }

        public static string GetCurrentISTTimestamp()
        {
            return DateTimeOffset.UtcNow
                .ToOffset(TimeSpan.FromHours(5.5))
                .ToString("dd/MM/yyyy hh:mm:ss tt", CultureInfo.InvariantCulture);
        }
    }
}