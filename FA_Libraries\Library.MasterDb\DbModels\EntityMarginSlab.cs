﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace Library.MasterDb.DbModels;

public class EntityMarginSlab
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    // public long LocationId { get; set; }
    public string Name { get; set; }

    public MarginEntity EntityType { get; set; }

    [NotMapped]
    public Dictionary<long, double> EntityMargins { get; set; }

    public string EntityMarginFlat
    {
        get => EntityMargins != null ? JsonConvert.SerializeObject(EntityMargins) : null;
        set => EntityMargins = value == null ? new Dictionary<long, double>() : JsonConvert.DeserializeObject<Dictionary<long, double>>(value);
    }

    public bool Deleted { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public MarginValueType ValueType { get; set; }

    [Column("MarginDescription")]
    public string MarginDescription { get; set; }

    public bool NotSendToApp { get; set; }
}
