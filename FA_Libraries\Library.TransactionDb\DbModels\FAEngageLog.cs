﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonModels;
using Newtonsoft.Json;

namespace Library.TransactionDb.DbModels;

[Table("FAEngageLogs")]
public class FAEngageLog
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public long NotificationId { get; set; }

    public long UserId { get; set; }

    public DateTimeOffset SendTime { get; set; }

    public Guid NotificationTransactionId { get; set; }

    public DateTime CreatedAt { get; set; }

    public bool IsReadByUser { get; set; }

    public DateTimeOffset? ReadByUserTime { get; set; }

    public bool IsSentFromBackend { get; set; }

    public string AggregationHierarchyJson { get; set; }

    public string SentMessage { get; set; }

    public Guid SentId { get; set; }

    [NotMapped]
    public List<EntityMinUser> AggregationHierarchy => JsonConvert.DeserializeObject<List<EntityMinUser>>(AggregationHierarchyJson);

    public bool IsInteracted { get; set; }

    public DateTimeOffset? InteractionTime { get; set; }
}
