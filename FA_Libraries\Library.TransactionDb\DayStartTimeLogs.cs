﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace Library.TransactionDb
{
    [Table("DayStartTimeLogs")]
    public class DayStartTimeLogs
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public int TimeSpentInSeconds { get; set; }

        public long UserId { get; set; }

        public string SessionId { get; set; }

        public DateTimeOffset DataPullStartedAt { get; set; }

        public DateTimeOffset DayStartedAt { get; set; }

        public DateTimeOffset DataPullEndedAt { get; set; }

        public DateTimeOffset CreatedAt { get; set; }
    }
}
