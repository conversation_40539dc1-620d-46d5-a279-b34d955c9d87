﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using CsvHelper;
using FileGenerator.Attributes;
using FileGenerator.Interfaces;
using Libraries.CommonEnums;

namespace FileGenerator
{
    public class CsvGenerator
    {
        private readonly bool _showAllColumns;
        private readonly bool _useNomenclature;
        private readonly Dictionary<string, object> companySettings;
        private readonly bool isSettingEnabled;
        private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
        private readonly Dictionary<string, bool> selectedCols;

        public CsvGenerator(CompanyNomenclatureSpecifier nomenclatureSpecifier = null, bool showAllColumns = false,
            bool isSettingEnabled = false, Dictionary<string, object> companySettings = null,
            Dictionary<string, bool> selectedCols = null)
        {
            this.nomenclatureSpecifier = nomenclatureSpecifier;
            _showAllColumns = showAllColumns;
            _useNomenclature = nomenclatureSpecifier != null;
            this.isSettingEnabled = isSettingEnabled;
            this.companySettings = companySettings;
            this.selectedCols = selectedCols;
        }

        private static bool CheckIfColNull<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            foreach (var item in listOfData)
            {
                if (colProp.GetValue(item) != null && !string.IsNullOrWhiteSpace(colProp.GetValue(item).ToString()))
                {
                    return false;
                }
            }

            return true;
        }

        private bool CheckIfColRequired<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            if (isSettingEnabled)
            {
                foreach (var item in listOfData)
                {
                    if (colProp.GetValue(item) != null && !string.IsNullOrWhiteSpace(colProp.GetValue(item).ToString()))
                    {
                        return true;
                    }
                    else
                    {
                        if (_showAllColumns)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }

            return false;
        }

        private bool CheckIfColRequiredBySetting(PropertyInfo colProp, CompanySetting[] companySettingsToCheck)
        {
            if (companySettings != null && companySettingsToCheck != null && companySettingsToCheck.Length > 0)
            {
                foreach (var companySetting in companySettingsToCheck)
                {
                    switch (companySetting)
                    {
                        case CompanySetting.UsesPositionCodes:
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !(bool)companySettings[companySetting.ToString()])
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.HighestPositionLevel:
                            PositionCodeLevel colPosLevel, highestPosLevel;
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !Enum.TryParse("Level" + colProp.Name[1], out colPosLevel) ||
                                !Enum.TryParse(companySettings[companySetting.ToString()].ToString(),
                                    out highestPosLevel) || highestPosLevel > colPosLevel)
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.HighestGeoHierarchy:
                            GeographyLevel colGeoLevel, highestGeoLevel;
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !Enum.TryParse(colProp.Name, out colGeoLevel) ||
                                !Enum.TryParse(companySettings[companySetting.ToString()].ToString(),
                                    out highestGeoLevel) || highestGeoLevel < colGeoLevel)
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.UsesFAUnify:
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !(bool)companySettings[companySetting.ToString()])
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.NotApplicable:
                        default:
                            return true;
                    }
                }
            }

            return true;
        }

        private static bool CheckIfColZero<T>(PropertyInfo colProp, IEnumerable<T> listOfData)
        {
            decimal value;
            double value2;
            foreach (var item in listOfData)
            {
                if (colProp.GetValue(item) != null && decimal.TryParse(colProp.GetValue(item).ToString(), out value) &&
                    value > 0)
                {
                    return false;
                }
                else if (colProp.GetValue(item) != null &&
                         double.TryParse(colProp.GetValue(item).ToString(), out value2) && value2 > 0)
                {
                    return false;
                }
            }

            return true;
        }

        public bool MakeCSV<T>(IEnumerable<T> listOfData, Stream stream, bool onlyExcelAttributes = true)
        {
            //Mar 23 2022; Asana: https://app.asana.com/0/1201786022217794/1201996151252532/f; Change: Use UTF8 Encoding for correctly showing special Characters
            TextWriter writeStream = new StreamWriter(stream, new UTF8Encoding(true));
            var csvWriter = new CsvWriter(writeStream, CultureInfo.InvariantCulture);
            var columns = typeof(T).GetProperties();
            //Headings
            var showDataForHeader = new Dictionary<int, bool>();
            var colNo = 0;
            var excelAttributeDic = new Dictionary<int, TableFieldAttribute>();
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                var excelAttrOfT = Tattr as TableFieldAttribute;
                excelAttributeDic.Add(colNo, excelAttrOfT);
                if (!onlyExcelAttributes || excelAttrOfT != null)
                {
                    var columnName = onlyExcelAttributes ? excelAttrOfT.ColumnName : colProp.Name;
                    if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                    {
                        var listforheader = columnName.Split(' ').ToList();
                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                        listforheader.Remove(listforheader[0]);
                        var striingotherthennomenclature = string.Join(" ", listforheader);
                        columnName = nomenclaturename + " " + striingotherthennomenclature;
                    }

                    if (_showAllColumns)
                    {
                        if (excelAttrOfT.ColumnRequirement == Requirement.SettingBased &&
                            !CheckIfColRequired(colProp, listOfData))
                        {
                            showDataForHeader.Add(colNo, false);
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                                 !CheckIfColRequiredBySetting(colProp,
                                     excelAttrOfT
                                         .CompanySettingsToCheck)) // showAllColumns is true in this block ref: ExcelGenerator
                        {
                            showDataForHeader.Add(colNo, false);
                        }
                        else
                        {
                            csvWriter.WriteField(columnName);
                            showDataForHeader.Add(colNo, true);
                        }
                    }
                    else
                    {
                        if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                            !CheckIfColRequiredBySetting(colProp,
                                excelAttrOfT
                                    .CompanySettingsToCheck)) // showAllColumns is true in this block ref: ExcelGenerator
                        {
                            showDataForHeader.Add(colNo, false);
                        }
                        else if (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && CheckIfColNull(colProp, listOfData))
                        {
                            showDataForHeader.Add(colNo, false);
                        }
                        else
                        {
                            csvWriter.WriteField(columnName);
                            showDataForHeader.Add(colNo, true);
                        }
                    }
                }
                else
                {
                    showDataForHeader.Add(colNo, false);
                }

                colNo++;
            }

            long flushSize = 100000;
            var flushPosition = flushSize;
            var itemNo = 1;

            foreach (var item in listOfData)
            {
                csvWriter.NextRecord();
                colNo = 0;
                foreach (var colProp in columns)
                {
                    var dataValue = colProp.GetValue(item);
                    // var tattr = Attribute.GetCustomAttribute(colProp, typeof(ExcelColumnAttribute));
                    //if (tattr != null && tattr is ExcelColumnAttribute)
                    if (showDataForHeader[colNo])
                    {
                        //var excelAttrOfT = (ExcelColumnAttribute)tattr;
                        if (dataValue != null)
                        {
                            if (dataValue.GetType() == typeof(DateTime) && excelAttributeDic[colNo] != null)
                            {
                                var dataStr = "";
                                var attr = excelAttributeDic[colNo];
                                if (!string.IsNullOrWhiteSpace(attr.CellFormat))
                                {
                                    dataStr = ((DateTime)dataValue).ToString(attr.CellFormat);
                                }
                                else
                                {
                                    dataStr = dataValue.ToString();
                                }

                                csvWriter.WriteField(dataStr);
                            }
                            else if (colProp.PropertyType == typeof(object))
                            {
                                csvWriter.WriteField(dataValue.ToString());
                            }
                            else
                            {
                                csvWriter.WriteField(dataValue);
                            }
                        }
                        else
                        {
                            csvWriter.WriteField("");
                        }
                    }

                    colNo++;
                }

                if (itemNo > flushPosition)
                {
                    writeStream.Flush();
                    stream.Flush();
                    flushPosition += flushSize;
                }

                itemNo++;
            }

            csvWriter.NextRecord();
            writeStream.Flush();
            stream.Flush();
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }

            return true;
        }

        public bool MakeCSV<T>(IEnumerable<T> listOfData, Stream stream, Dictionary<int, bool> showDataForHeader,
            Dictionary<int, TableFieldAttribute> excelAttributeDic, int rowIndex = 2, bool onlyExcelAttributes = true)
        {
            TextWriter writeStream = new StreamWriter(stream, new UTF8Encoding(true));
            var csvWriter = new CsvWriter(writeStream, CultureInfo.InvariantCulture);
            var columns = typeof(T).GetProperties();
            //Headings
            var colNo = 0;
            // we make header in first call only - if data is 0 in first call then will create header in next call when data is available
            var createheader = rowIndex == 2 && listOfData.Count() > 0;
            if (createheader)
            {
                foreach (var colProp in columns)
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    var excelAttrOfT = Tattr as TableFieldAttribute;
                    excelAttributeDic.Add(colNo, excelAttrOfT);
                    if (!onlyExcelAttributes || excelAttrOfT != null)
                    {
                        var columnName = onlyExcelAttributes ? excelAttrOfT.ColumnName : colProp.Name;
                        if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                        {
                            var listforheader = columnName.Split(' ').ToList();
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                            listforheader.Remove(listforheader[0]);
                            var striingotherthennomenclature = string.Join(" ", listforheader);
                            columnName = nomenclaturename + " " + striingotherthennomenclature;
                        }

                        if (_showAllColumns)
                        {
                            if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                                !CheckIfColRequiredBySetting(colProp,
                                    excelAttrOfT
                                        .CompanySettingsToCheck)) // showAllColumns is true in this block ref: ExcelGenerator
                            {
                                showDataForHeader.Add(colNo, false);
                            }
                            else
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                        }
                        else
                        {
                            if (excelAttrOfT.ColumnRequirement == Requirement.SpecificSettingBased &&
                                !CheckIfColRequiredBySetting(colProp,
                                    excelAttrOfT
                                        .CompanySettingsToCheck)) // showAllColumns is true in this block ref: ExcelGenerator
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.HideIfZero &&
                                     !CheckIfColZero(colProp, listOfData))
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.Selected &&
                                     selectedCols.ContainsKey(colProp.Name))
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull &&
                                !CheckIfColNull(colProp, listOfData))
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.Required)
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                            else if (excelAttrOfT.ColumnRequirement == Requirement.SettingBased && isSettingEnabled)
                            {
                                csvWriter.WriteField(columnName);
                                showDataForHeader.Add(colNo, true);
                            }
                            else
                            {
                                showDataForHeader.Add(colNo, false);
                            }
                        }
                    }
                    else
                    {
                        showDataForHeader.Add(colNo, false);
                    }

                    colNo++;
                }

                csvWriter.NextRecord();
            }

            long flushSize = 100000;
            var flushPosition = flushSize;
            var itemNo = 1;

            var rowcount = 1;
            foreach (var item in listOfData)
            {
                colNo = 0;
                foreach (var colProp in columns)
                {
                    var dataValue = colProp.GetValue(item);
                    // var tattr = Attribute.GetCustomAttribute(colProp, typeof(ExcelColumnAttribute));
                    //if (tattr != null && tattr is ExcelColumnAttribute)
                    if (showDataForHeader[colNo])
                    {
                        //var excelAttrOfT = (ExcelColumnAttribute)tattr;
                        if (dataValue != null)
                        {
                            if (dataValue.GetType() == typeof(DateTime) && excelAttributeDic[colNo] != null)
                            {
                                var dataStr = "";
                                var attr = excelAttributeDic[colNo];
                                if (!string.IsNullOrWhiteSpace(attr.CellFormat))
                                {
                                    dataStr = ((DateTime)dataValue).ToString(attr.CellFormat);
                                }
                                else
                                {
                                    dataStr = dataValue.ToString();
                                }

                                csvWriter.WriteField(dataStr);
                            }
                            else if (colProp.PropertyType == typeof(object))
                            {
                                csvWriter.WriteField(dataValue.ToString());
                            }
                            else
                            {
                                csvWriter.WriteField(dataValue);
                            }
                        }
                        else
                        {
                            csvWriter.WriteField("");
                        }
                    }

                    colNo++;
                }

                if (itemNo > flushPosition)
                {
                    writeStream.Flush();
                    stream.Flush();
                    flushPosition += flushSize;
                }

                itemNo++;
                if (rowcount < listOfData.Count())
                {
                    csvWriter.NextRecord();
                }

                rowcount++;
            }

            if (listOfData.Count() != 0)
            {
                csvWriter.NextRecord();
            }

            writeStream.Flush();
            stream.Flush();
            return true;
        }

        public bool MakeCSV<T>(IEnumerable<T> listOfData, Stream stream, Dictionary<string, string> renamedColumns,
            bool onlyExcelAttributes = true)
        {
            TextWriter writeStream = new StreamWriter(stream, new UTF8Encoding(true));
            var csvWriter = new CsvWriter(writeStream, CultureInfo.InvariantCulture);
            var columns = typeof(T).GetProperties();
            //Headings
            var showDataForHeader = new Dictionary<int, bool>();
            var colNo = 0;
            var excelAttributeDic = new Dictionary<int, TableFieldAttribute>();
            foreach (var colProp in columns)
            {
                var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                var excelAttrOfT = Tattr as TableFieldAttribute;
                excelAttributeDic.Add(colNo, excelAttrOfT);
                if (!onlyExcelAttributes || excelAttrOfT != null)
                {
                    var columnName = onlyExcelAttributes ? excelAttrOfT.ColumnName : colProp.Name;
                    if (renamedColumns.ContainsKey(columnName))
                    {
                        columnName = renamedColumns[columnName];
                    }

                    if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                    {
                        var listforheader = columnName.Split(' ').ToList();
                        var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                        listforheader.Remove(listforheader[0]);
                        var striingotherthennomenclature = string.Join(" ", listforheader);
                        columnName = nomenclaturename + " " + striingotherthennomenclature;
                    }

                    if (_showAllColumns)
                    {
                        csvWriter.WriteField(columnName);
                        showDataForHeader.Add(colNo, true);
                    }
                    else
                    {
                        if (!CheckIfColNull(colProp, listOfData))
                        {
                            csvWriter.WriteField(columnName);
                            showDataForHeader.Add(colNo, true);
                        }
                        else
                        {
                            showDataForHeader.Add(colNo, false);
                        }
                    }
                }
                else
                {
                    showDataForHeader.Add(colNo, false);
                }

                colNo++;
            }

            long flushSize = 100000;
            var flushPosition = flushSize;
            var itemNo = 1;

            foreach (var item in listOfData)
            {
                csvWriter.NextRecord();
                colNo = 0;
                foreach (var colProp in columns)
                {
                    var dataValue = colProp.GetValue(item);
                    // var tattr = Attribute.GetCustomAttribute(colProp, typeof(ExcelColumnAttribute));
                    //if (tattr != null && tattr is ExcelColumnAttribute)
                    if (showDataForHeader[colNo])
                    {
                        //var excelAttrOfT = (ExcelColumnAttribute)tattr;
                        if (dataValue != null)
                        {
                            if (dataValue.GetType() == typeof(DateTime) && excelAttributeDic[colNo] != null)
                            {
                                var dataStr = "";
                                var attr = excelAttributeDic[colNo];
                                if (!string.IsNullOrWhiteSpace(attr.CellFormat))
                                {
                                    dataStr = ((DateTime)dataValue).ToString(attr.CellFormat);
                                }
                                else
                                {
                                    dataStr = dataValue.ToString();
                                }

                                csvWriter.WriteField(dataStr);
                            }
                            else
                            {
                                csvWriter.WriteField(dataValue);
                            }
                        }
                        else
                        {
                            csvWriter.WriteField("");
                        }
                    }

                    colNo++;
                }

                if (itemNo > flushPosition)
                {
                    writeStream.Flush();
                    stream.Flush();
                    flushPosition += flushSize;
                }

                itemNo++;
            }

            csvWriter.NextRecord();
            writeStream.Flush();
            stream.Flush();
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }

            return true;
        }

        public bool MakeCSVForPSOD<T>(IEnumerable<T> listOfData, Stream stream, Dictionary<int, bool> showDataForHeader,
            Dictionary<int, TableFieldAttribute> excelAttributeDic, out List<string> colsToDelete, int rowIndex = 2, bool onlyExcelAttributes = true, bool callWithoutBatching = false,
            Dictionary<string, string> excelColumnNameNomenclatureDict = null, bool useNomenclatureForHeader = true)
        {
            TextWriter writeStream = new StreamWriter(stream, new UTF8Encoding(true));
            var csvWriter = new CsvWriter(writeStream, CultureInfo.InvariantCulture);
            var allColumns = typeof(T).GetProperties();
            var columns = selectedCols != null && selectedCols.Count > 0
                ? allColumns.Where(p => selectedCols.ContainsKey(p.Name))
                : allColumns;
            var isSelectColumnFunctionalityUsed = columns.Count() < allColumns.Count();
            if (excelColumnNameNomenclatureDict == null)
            {
                excelColumnNameNomenclatureDict = new Dictionary<string, string>();
            }
            //Headings
            var colNo = 0;
            colsToDelete = new List<string>();
            // we make header in first call only - if data is 0 in first call then will create header in next call when data is available
            var createheader = rowIndex == 2 && listOfData.Count() > 0;
            if (createheader)
            {
                foreach (var colProp in columns)
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    var excelAttrOfT = Tattr as TableFieldAttribute;
                    excelAttributeDic.Add(colNo, excelAttrOfT);
                    if (!onlyExcelAttributes || excelAttrOfT != null)
                    {
                        var columnName = onlyExcelAttributes ? excelAttrOfT.ColumnName : colProp.Name;
                        if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                        {
                            var listforheader = columnName.Split(' ').ToList();
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                            listforheader.Remove(listforheader[0]);
                            var stringOtherThenNomenclature = string.Join(" ", listforheader);
                            var colNomenclatureName = nomenclaturename + (string.IsNullOrWhiteSpace(stringOtherThenNomenclature) ? "" : " " + stringOtherThenNomenclature);
                            excelColumnNameNomenclatureDict.Add(columnName, colNomenclatureName);
                            columnName = useNomenclatureForHeader ? colNomenclatureName : columnName;
                        }

                        //columns to delete          
                        if (!isSelectColumnFunctionalityUsed && ((excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && CheckIfColZero(colProp, listOfData))
                            || (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && CheckIfColNull(colProp, listOfData))))
                        {
                            colsToDelete.Add(columnName);
                        }

                        if (_showAllColumns && callWithoutBatching &&
                            ((excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && CheckIfColZero(colProp, listOfData))
                            || (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && CheckIfColNull(colProp, listOfData))))
                        {
                            showDataForHeader.Add(colNo, false);
                        }
                        else
                        {
                            csvWriter.WriteField(columnName);
                            showDataForHeader.Add(colNo, true);
                        }
                    }
                    else
                    {
                        showDataForHeader.Add(colNo, false);
                    }

                    colNo++;
                }

                csvWriter.NextRecord();
            }
            else
            {
                foreach (var colProp in columns)
                {
                    var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                    var excelAttrOfT = Tattr as TableFieldAttribute;
                    if (!onlyExcelAttributes || excelAttrOfT != null)
                    {
                        var columnName = onlyExcelAttributes ? excelAttrOfT.ColumnName : colProp.Name;
                        if (_useNomenclature && excelAttrOfT.NomenclatureRequirement)
                        {
                            var listforheader = columnName.Split(' ').ToList();
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(listforheader[0]);
                            listforheader.Remove(listforheader[0]);
                            var striingotherthennomenclature = string.Join(" ", listforheader);
                            var colNomenclatureName = nomenclaturename + (string.IsNullOrWhiteSpace(striingotherthennomenclature) ? "" : " " + striingotherthennomenclature);
                            columnName = useNomenclatureForHeader ? colNomenclatureName : columnName;
                        }
                        //columns to delete          
                        if (!isSelectColumnFunctionalityUsed && ((excelAttrOfT.ColumnRequirement == Requirement.HideIfZero && CheckIfColZero(colProp, listOfData))
                            || (excelAttrOfT.ColumnRequirement == Requirement.HideIfNull && CheckIfColNull(colProp, listOfData))))
                        {
                            colsToDelete.Add(columnName);
                        }
                    }
                }
            }

            long flushSize = 100000;
            var flushPosition = flushSize;
            var itemNo = 1;

            var rowcount = 1;
            foreach (var item in listOfData)
            {
                colNo = 0;
                foreach (var colProp in columns)
                {
                    var dataValue = colProp.GetValue(item);
                    // var tattr = Attribute.GetCustomAttribute(colProp, typeof(ExcelColumnAttribute));
                    //if (tattr != null && tattr is ExcelColumnAttribute)
                    if (showDataForHeader[colNo])
                    {
                        //var excelAttrOfT = (ExcelColumnAttribute)tattr;
                        if (dataValue != null)
                        {
                            if (dataValue.GetType() == typeof(DateTime) && excelAttributeDic[colNo] != null)
                            {
                                var dataStr = "";
                                var attr = excelAttributeDic[colNo];
                                if (!string.IsNullOrWhiteSpace(attr.CellFormat))
                                {
                                    dataStr = ((DateTime)dataValue).ToString(attr.CellFormat);
                                }
                                else
                                {
                                    dataStr = dataValue.ToString();
                                }

                                csvWriter.WriteField(dataStr);
                            }
                            else
                            {
                                csvWriter.WriteField(dataValue);
                            }
                        }
                        else
                        {
                            csvWriter.WriteField("");
                        }
                    }

                    colNo++;
                }

                if (itemNo > flushPosition)
                {
                    writeStream.Flush();
                    stream.Flush();
                    flushPosition += flushSize;
                }

                itemNo++;
                if (rowcount < listOfData.Count())
                {
                    csvWriter.NextRecord();
                }

                rowcount++;
            }

            if (listOfData.Count() != 0)
            {
                csvWriter.NextRecord();
            }

            writeStream.Flush();
            stream.Flush();
            return true;
        }
        public bool MakeCSV(Stream stream, params object[] datasets)
        {
            TextWriter writeStream = new StreamWriter(stream, new UTF8Encoding(true));
            var csvWriter = new CsvWriter(writeStream, CultureInfo.InvariantCulture);

            long flushSize = 100000;

            foreach (var dataset in datasets)
            {
                if (dataset == null)
                {
                    continue;
                }

                if (dataset is DataTable dt)
                {
                    for (var i = 0; i < dt.Columns.Count; i++)
                    {
                        var columnName = dt.Columns[i].ColumnName;

                        if (_useNomenclature)
                        {
                            var listForHeader = columnName.Split(' ').ToList();
                            var nomenclatureName = nomenclatureSpecifier.GetHeaderName(listForHeader[0]);
                            listForHeader.RemoveAt(0);
                            var remaining = string.Join(" ", listForHeader);
                            columnName = nomenclatureName + (string.IsNullOrWhiteSpace(remaining) ? "" : " " + remaining);
                        }

                        csvWriter.WriteField(columnName);
                    }

                    csvWriter.NextRecord();

                    var rowNo = 1;
                    var flushPosition = flushSize;
                    foreach (DataRow row in dt.Rows)
                    {
                        for (var i = 0; i < dt.Columns.Count; i++)
                        {
                            var value = row[i];
                            if (value != null && value != DBNull.Value)
                            {
                                if (value is DateTime dtValue)
                                {
                                    csvWriter.WriteField(dtValue.ToString("dd-MM-yyyy"));
                                }
                                else
                                {
                                    csvWriter.WriteField(value.ToString());
                                }
                            }
                            else
                            {
                                csvWriter.WriteField("");
                            }
                        }

                        csvWriter.NextRecord();

                        if (rowNo > flushPosition)
                        {
                            writeStream.Flush();
                            stream.Flush();
                            flushPosition += flushSize;
                        }

                        rowNo++;
                    }
                }
                else
                {
                    var type = dataset.GetType();
                    var ienumType = type.GetInterfaces()
                        .FirstOrDefault(t => t.IsGenericType && t.GetGenericTypeDefinition() == typeof(IEnumerable<>));

                    if (ienumType != null)
                    {
                        var elementType = ienumType.GetGenericArguments()[0];
                        var method = typeof(ExcelGenerator).GetMethod(nameof(MakeCSV),
                            new[] { typeof(IEnumerable<>).MakeGenericType(elementType), typeof(Stream), typeof(bool) });

                        if (method != null)
                        {
                            method.Invoke(this, new object[] { dataset, stream, true });
                        }
                    }
                }

                csvWriter.NextRecord();
                csvWriter.NextRecord();
            }

            writeStream.Flush();
            stream.Flush();
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }

            return true;
        }
    }
}
