﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    [Table("ApprovalEntities")]
    public class NewApprovalEntities
    {
        [Column("Id")]
        public int Id { get; set; }

        [Column("EntityEnum")]
        public ApprovalEngineRequestType EntityEnum { get; set; }

        [Column("EntityName")]
        [MaxLength(100)]
        public string EntityName { get; set; }

        [Column("EntityType")]
        public int EntityType { get; set; }
    }
}
