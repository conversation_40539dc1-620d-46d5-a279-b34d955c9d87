﻿// Copyright (c) FieldAssist. All Rights Reserved.

using SlackNet.WebApi;
using System.Threading.Tasks;
using System.IO;

namespace Library.SlackService.Interfaces
{
    /// <summary>
    /// Provides methods for interacting with Slack channels, including sending messages and uploading files.
    /// </summary>
    public interface ISlackBotService
    {
        /// <summary>
        /// Sends a file to a specified Slack channel. Optionally compresses the file before sending.
        /// </summary>
        /// <param name="logFile">The file to send.</param>
        /// <param name="channel">The Slack channel ID or name.</param>
        /// <param name="compressFile">If true, compresses the file before sending.</param>
        Task SendFileToChannel(FileInfo logFile, string channel, bool compressFile = false);

        /// <summary>
        /// Sends a text message to a specified Slack channel.
        /// </summary>
        /// <param name="message">The message text to send.</param>
        /// <param name="channel">The Slack channel ID or name.</param>
        Task SendMessageToChannel(string message, string channel, string iconEmoji = ":fa:", string userName = "FA Slack Bot");

        /// <summary>
        /// Sends a Slack message using a <see cref="Message"/> object.
        /// </summary>
        /// <param name="message">The <see cref="Message"/> object containing message details.</param>
        Task SendMessage(Message message);
        Task JoinChannel(string channelId);
    }
}
