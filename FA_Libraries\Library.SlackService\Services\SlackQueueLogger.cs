﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Text.Json;
using Library.Infrastructure.QueueService;
using Library.SlackService.Helpers;
using Library.SlackService.Interfaces;
using SlackNet;
using SlackNet.Blocks;
using SlackNet.WebApi;

namespace Library.SlackService.Services;

public static class SlackQueues
{
    public const string SlackInfoQueue = "slack-info-queue";
    public const string SlackErrorQueue = "slack-error-queue";
}

public class SlackQueueLogger : ISlackLogger
{
    private readonly string _channel;
    private readonly QueueHandlerService _queueHandlerService;
    private readonly string _username;

    public SlackQueueLogger(QueueHandlerService queueHandlerService, string username, string channel)
    {
        _channel = channel;
        _queueHandlerService = queueHandlerService;
        _username = username;
    }

    public void LogInfo(string logMessage, object metaData = null)
    {
        try
        {
            var slackMessage = new Message
            {
                Channel = _channel,
                Username = _username,
                Attachments = new List<Attachment>
            {
                new Attachment
                {
                    Color = "blue",
                    Blocks= new List<Block>
                    {
                        new HeaderBlock
                        {
                            Text = logMessage
                        }
                    }
                },
                new Attachment
                {
                    Color = "blue",
                    Blocks= new List<Block>
                    {
                        new SectionBlock
                        {
                            Fields = SlackLogHelperMethods.GetFieldsForMetaData(metaData)
                        },
                    }
                }
            }
            };
            _queueHandlerService.AddToQueue(SlackQueues.SlackInfoQueue, slackMessage).Wait();
        }
        catch (Exception loggingException)
        {
            Console.WriteLine($"Failed to add log to queue. Error:\n{loggingException.Message}");
            Console.Error.WriteLine($"{logMessage}\n{JsonSerializer.Serialize(metaData)}");
        }
    }

    public void LogError(string logMessage, Exception ex, object metaData = null)
    {
        try
        {
            var slackMessage = new Message
            {
                Channel = _channel,
                Username = _username,
                Attachments = new List<Attachment>
            {
                new Attachment
                {
                    Color = "danger",
                    Blocks= new List<Block>
                    {
                        new HeaderBlock
                        {
                            Text = logMessage
                        }
                    }
                },
                new Attachment
                {
                    Color = "danger",
                    Blocks= new List<Block>
                    {
                        new SectionBlock
                        {
                            Fields = SlackLogHelperMethods.GetFieldsForMetaData(metaData)
                        },
                    }
                },
                new Attachment
                {
                    Color = "danger",
                    Blocks= new List<Block>
                    {
                        new SectionBlock
                        {
                            Fields = SlackLogHelperMethods.GetFieldsForException(ex)
                        },
                    }
                }
            }
            };
            _queueHandlerService.AddToQueue(SlackQueues.SlackErrorQueue, slackMessage).Wait();
        }
        catch (Exception loggingException)
        {
            Console.WriteLine($"Failed to add log to queue. Error:\n{loggingException.Message}");
            Console.Error.WriteLine($"{logMessage}\n{JsonSerializer.Serialize(metaData)}\n{ex}");
        }
    }
}
