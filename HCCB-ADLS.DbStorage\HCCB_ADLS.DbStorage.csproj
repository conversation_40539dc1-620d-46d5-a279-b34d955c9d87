﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
	
	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="EFCore.BulkExtensions" Version="8.0.2" />
	</ItemGroup>
	
	<ItemGroup>
	  <ProjectReference Include="..\FA_Libraries\Libraries.Authentication\AuditHelper.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
	  <ProjectReference Include="..\FA_Libraries\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.SqlHelper\Library.SqlHelper.csproj" />
	  <ProjectReference Include="..\HCCB_ADLS.Core\HCCB_ADLS.Core.csproj" />
	</ItemGroup>

</Project>
