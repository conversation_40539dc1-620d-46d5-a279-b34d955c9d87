﻿using Core.APIHelpers;
using Core.Loggers;
using Core.Models;
using Core.Models.CTS;
using Core.Models.MasterDbModels;
using Core.Repositories;
using CTSStatusProcessor.Models;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using Libraries.CommonEnums;
using Library.CommonHelpers;
using Polly;
using System.Globalization;
using System.Net.Http.Headers;
using System.Text.Json;

namespace CTSStatusProcessor.Services
{
    public class CtsService
    {
        private readonly HttpClient hccbHttpClient;
        private readonly IReuestTimelineRepository reuestTimelineRepository;
        private readonly ITransactionRepository transactionRepository;
        private readonly IHttpClientFactory httpClientFactory;
        private readonly HttpRequestHelper httpRequestHelper;
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly CtsHccbApiValues hccbApiValues;
        private const long UATCompanyId = 193017;

        public CtsService(HttpClient hccbHttpClient,IReuestTimelineRepository reuestTimelineRepository, ITransactionRepository transactionRepository,
        IHttpClientFactory httpClientFactory, HttpRequestHelper httpRequestHelper, ISlackLogHelper slackLogHelper, CtsHccbApiValues hccbApiValues)
        {
            this.hccbHttpClient = hccbHttpClient;
            this.transactionRepository = transactionRepository;
            this.reuestTimelineRepository = reuestTimelineRepository;
            this.httpClientFactory = httpClientFactory;
            this.httpRequestHelper = httpRequestHelper;
            _slackLogHelper = slackLogHelper;
            this.hccbApiValues = hccbApiValues;
        }

        public async Task ProcessRequestStatus(QueueData queueData)
        {
            var existingTimeline = await reuestTimelineRepository.GetRequestTimelineForStatus(queueData.CompanyId, queueData.RequestId, queueData.ApprovalEngineRequestType, ApprovalEngineRequestStatus.ExternalPending);
            if (existingTimeline == null)
            {
                throw new Exception($"Request Timeline data not found for request {queueData.RequestId}");
            }
            var requestData = await GetRequestData(queueData);
            if (requestData == null)
            {
                throw new Exception($"Request data not found for request {queueData.RequestId}");
            }
            var appUid = GetAppUid(queueData, requestData);
            CTSStatusResponse requestStatus = await GetRequestStatusFromCtsApi(queueData, existingTimeline, appUid);
            if (requestStatus.data != null && requestStatus.data.Count > 0)
            {
                var request = requestStatus.data.Where(x => x.APP_UID == appUid).FirstOrDefault();
                if (request == null)
                {
                    throw new Exception($"Request not found {queueData.RequestId} with appUid {appUid} in CTS response");
                }

                const int MaxStatusLength = 100;
                var statusString = request.CTS_STATUS_DETAIL?.Length > MaxStatusLength
                    ? request.CTS_STATUS_DETAIL.Substring(0, 100)
                    : request.CTS_STATUS_DETAIL ?? string.Empty;

                await reuestTimelineRepository.UpdateAndApproveRequest(existingTimeline, statusString);

            }
        }
        private string GetAppUid(QueueData queueData, dynamic requestData) 
        { 
            return queueData.ApprovalEngineRequestType == ApprovalEngineRequestType.AssetReallocation && requestData?.AssetReallocationType != null 
                ? $"{queueData.RequestId}_{(int)queueData.ApprovalEngineRequestType}_{(int)requestData.AssetReallocationType}" 
                : $"{queueData.RequestId}_{(int)queueData.ApprovalEngineRequestType}"; 
        }

        private async Task<dynamic?> GetRequestData(QueueData queueData)
        {
            return queueData.ApprovalEngineRequestType switch
            {
                ApprovalEngineRequestType.AssetAllocation => await transactionRepository.GetAssetAllocationRequestById(queueData.RequestId, queueData.CompanyId),
                ApprovalEngineRequestType.AssetReallocation => await transactionRepository.GetAssetRellocationRequestById(queueData.RequestId, queueData.CompanyId),
                _ => null
            };
        }
        public async Task<CTSStatusResponse> GetRequestStatusFromCtsApi(QueueData queueData, RequestApprovalTimeline requestData, string appUid)
        {
            var token = await GetAuthToken(queueData.CompanyId);
            hccbHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            var url = hccbApiValues.ctsStatusUrl;
            if (queueData.CompanyId == UATCompanyId)
            {
                url = hccbApiValues.ctsStatusUrl_UAT;
            }
            
            var statusInput = new CTSStatusInput
            {
                page_number = "1",
                app_uid = appUid,
                from_date = requestData.CreatedAt.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture),
                to_date = requestData.CreatedAt.AddDays(1).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture)
            };
            var input = JsonSerializer.Serialize(statusInput);
            var content = new StringContent(input, System.Text.Encoding.UTF8, "application/json");
            return await GetApiResponse(url, token, content);
        }
        private async Task<CTSStatusResponse> GetApiResponse(string url, string token, StringContent content)
        {
            hccbHttpClient.DefaultRequestHeaders.Clear();
            hccbHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            var response = await Policy
                .Handle<HttpRequestException>()
                .OrResult<HttpResponseMessage>(r =>
                {
                    var responseString = r.Content.ReadAsStringAsync().Result;
                    var errorResponse = JsonSerializer.Deserialize<HccbErrorResponse>(responseString);
                    return !r.IsSuccessStatusCode && errorResponse?.error?.detail?.code != "404";
                })
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                async (exception, timeSpan, retryCount, context) =>
                {
                    await _slackLogHelper.SendMessageToSlack($"Retry {retryCount} due to error: {exception.Exception?.Message ?? exception.Result.ReasonPhrase}");
                })
                .ExecuteAsync(() => hccbHttpClient.PostAsync(url, content));

            var responseString = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                await _slackLogHelper.SendMessageToSlack($"Error getting Cts data: {responseString}");
                throw new Exception($"Error getting Cts data: {responseString}");
            }
            return JsonSerializer.Deserialize<CTSStatusResponse>(responseString);
        }
        private async Task<string> GetAuthToken(long companyId)
        {
            _ = new Dictionary<string, string>();
            string token;
            string url;
            Dictionary<string, string> postData;
            if (companyId == UATCompanyId)
            {
                postData = new Dictionary<string, string>
                {
                    { "grant_type", "client_credentials" },
                    { "client_id", hccbApiValues.clientId_UAT },
                    { "client_secret", hccbApiValues.clientSecret_UAT }
                };
                token = hccbApiValues.accessToken_UAT;
                url = hccbApiValues.tokenUrl_UAT;
            }
            else
            {
                postData = new Dictionary<string, string>
                {
                    { "grant_type", "client_credentials" },
                    { "client_id", hccbApiValues.clientId },
                    { "client_secret", hccbApiValues.clientSecret }
                };
                token = hccbApiValues.accessToken;
                url = hccbApiValues.tokenUrl;
            }

            var content = new FormUrlEncodedContent(postData);
            hccbHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await hccbHttpClient.PostAsync(url, content);
            await response.EnsureSuccessStatusCodeAsync();
            var jsonResponse = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<OAuthResponse>(jsonResponse).AccessToken;
        }
    }
}
