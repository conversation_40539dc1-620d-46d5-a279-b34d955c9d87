﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;

namespace Library.EmailService
{
    public class EmailHandlerGulf : IDisposable
    {
        public EmailHandlerGulf()
        {
            // Set the security protocol globally.  
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            _emailClient = new SmtpClient(EmailHost, EmailPort)
            {
                EnableSsl = true,
                Credentials = new NetworkCredential(EmailUsername, EmailPassword),
                Timeout = 6000
            };
        }

#pragma warning disable CA1822 // Mark members as static
        public async Task SendEmail(string to, string subject, string body, string fromEmail, string fromName, bool isHTML = false, string bcc = "", string cc = "")
#pragma warning restore CA1822 // Mark members as static
        {
            var noReplyEmailAddress = new MailAddress(fromEmail, fromName);

            if (fromEmail.EndsWith("@fieldassist.in"))
            {
                noReplyEmailAddress = new MailAddress(fromEmail, string.IsNullOrWhiteSpace(fromName) ? "FieldAssist" : fromName);
            }

            var message = new MailMessage
            {
                Body = body,
                Subject = subject,
                From = noReplyEmailAddress,
                IsBodyHtml = true
            };
            to.Split(",;".ToCharArray()).ToList().ForEach(t => message.To.Add(new MailAddress(t)));
            if (!string.IsNullOrWhiteSpace(bcc))
            {
                bcc.Split(",;".ToCharArray()).ToList().ForEach(t => message.Bcc.Add(new MailAddress(t)));
            }

            if (!string.IsNullOrWhiteSpace(cc))
            {
                cc.Split(",;".ToCharArray()).ToList().ForEach(t => message.CC.Add(new MailAddress(t)));
            }

            using (var smtp = new SmtpClient(EmailHost, EmailPort))
            {
                smtp.EnableSsl = true;
                smtp.Credentials = new NetworkCredential(EmailUsername, EmailPassword);
                smtp.Timeout = 6000;

                await smtp.SendMailAsync(message);
            }
        }

        public void Dispose()
        {
            _emailClient.Dispose();
        }

        public const string EmailHost = "email-smtp.ap-south-1.amazonaws.com";
        public const int EmailPort = 587;
        public const string EmailUsername = "AKIAQFU32FLHX5UIX36V";
        public const string EmailPassword = "BHV6FxdSfrjiQwMBaKp4G2B9xFmPAfMxGrPXrSygSXOh";

        public const int RetryCount = 5;

        private readonly SmtpClient _emailClient;
    }
}
