﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    public class ApprovalRequest
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public ApprovalEngineRequestType RequestType { get; set; }
        public ApprovalEngineRequestStatus RequestStatus { get; set; }
        public long? EntityId { get; set; }
        public long RequestorEmployeeId { get; set; }
        public long RequestorPositionId { get; set; }
        public Guid? RequestDetailsGuid { get; set; }
        public DateTime CreatedAt { get; set; }

        [MaxLength(250)]
        public string? CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
}
