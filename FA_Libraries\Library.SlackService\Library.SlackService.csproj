﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
<OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SlackNet" Version="0.17.2" />
    <PackageReference Include="SlackNet.Extensions.DependencyInjection" Version="0.17.2" />
    <PackageReference Include="Polly" Version="8.4.1" />
  </ItemGroup>
    
  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Library.CommonHelpers\Library.CommonHelpers.csproj" />
    <ProjectReference Include="..\Library.Infrastructure\Library.Infrastructure.csproj" />
    <ProjectReference Include="..\Library.StorageWriter\Library.StorageWriter.csproj" />
  </ItemGroup>

</Project>
