﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Concurrent;

namespace Library.Logs.Services
{
    public class FileLogger : IDisposable
    {
        public FileLogger(bool initializeManually = false)
        {
            if (!initializeManually)
            {
                InitializeAsync(Guid.NewGuid().ToString());
            }
        }
        private string? LogFileNameFull => $"{s_logsDirectory}{_logFileName}";
        private string? _logFileName;
        private string? _metadataToAddToLogs;
        private FileStream? _logFileStream;
        private StreamWriter? _logFileWriter;
        private Task? _processQueueTask;

        private readonly BlockingCollection<string> _entryQueue = new BlockingCollection<string>(1024);
        public static readonly string s_logsDirectory = $"{Path.GetTempPath()}{Path.DirectorySeparatorChar}Logs{Path.DirectorySeparatorChar}";

        public Task InitializeAsync(string logFileName, bool append = false, string metadataToAddToLogs = "")
        {
            if (string.IsNullOrWhiteSpace(logFileName))
            {
                throw new ArgumentNullException("logFileName", "logFileName cannot be null. First initialize FileLogger using Initialize Method");
            }

            if (!string.IsNullOrWhiteSpace(_logFileName))
            {
                Console.WriteLine($"FileLogger instance is already initialized with file {_logFileName}. Dispose it off and create a new one.");
            }

            _logFileName = logFileName;
            _metadataToAddToLogs = metadataToAddToLogs;

            CreateFile(append);

            _processQueueTask = Task.Run(() => ProcessQueue());

            return Task.CompletedTask;
        }

        private void CreateFile(bool append)
        {
#pragma warning disable CS8604 // Possible null reference argument.
            var fileInfo = new FileInfo(LogFileNameFull);
#pragma warning restore CS8604 // Possible null reference argument.
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            fileInfo.Directory.Create();
#pragma warning restore CS8602 // Dereference of a possibly null reference.

            _logFileStream = new FileStream(LogFileNameFull, FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read);
            if (append)
            {
                _logFileStream.Seek(0, SeekOrigin.End);
            }
            else
            {
                _logFileStream.SetLength(0); // clear the file
            }

            _logFileWriter = new StreamWriter(_logFileStream);
        }

        private void WriteMessage(string message, bool flush)
        {
            if (_logFileWriter != null)
            {
                _logFileWriter.WriteLine(message);
                if (flush)
                {
                    _logFileWriter.Flush();
                }
            }
        }

        private Task ProcessQueue()
        {
            foreach (var message in _entryQueue.GetConsumingEnumerable())
            {
                try
                {
                    WriteMessage(message, _entryQueue.Count == 0);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to write into file {LogFileNameFull}.\n{ex}");
                }
            }

            return Task.CompletedTask;
        }

        public void WriteLine(string message, bool skipConsoleLog = false, bool truncateConsoleMessage = false, short truncatedLength = 256)
        {
            if (string.IsNullOrWhiteSpace(_logFileName))
            {
                throw new ArgumentNullException("LogFileName cannot be null. First initialize it using Initialize Method");
            }

            if (!_entryQueue.IsAddingCompleted)
            {
                message = $"[{DateTime.UtcNow.AddMinutes(330)}]: {_metadataToAddToLogs} {message}";
                if (!skipConsoleLog)
                {
                    Console.WriteLine(truncateConsoleMessage ? message.Substring(0, Math.Min(truncatedLength, message.Length)) : message);
                }

                try
                {
                    _entryQueue.Add(message);
                    return;
                }
                catch (InvalidOperationException) { }
            }
            else
            {
                throw new ObjectDisposedException("Cannot log in a disposed FileLogger");
            }
        }

        public async Task FlushAsync()
        {
            if (_processQueueTask != null)
            {
                Console.WriteLine($"Waiting for logging all entries to File {LogFileNameFull}!!");
                await _processQueueTask;
                Console.WriteLine($"Finished logging all entries to File!! {LogFileNameFull}");
            }
        }

        public void Dispose()
        {
            _entryQueue.CompleteAdding();
            FlushAsync().Wait();
            _logFileWriter?.Dispose();
        }
    }
}
