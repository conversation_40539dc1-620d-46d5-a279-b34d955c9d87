﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using Libraries.CommonEnums;

namespace Library.TransactionDb.DbModels
{
    [Table("LMSLeadActivitiesTransactions")]
    public class LMSLeadActivityTransactions
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long CompanyId { get; set; }

        [Required]
        public long LeadId { get; set; }

        [Required]
        public LMSActivityType ActivityType { get; set; }

        [Required]
        public LMSActivitySource Source { get; set; }

        [StringLength(255)]
        public string Title { get; set; }

        public string Description { get; set; }

        public string Remark { get; set; }

        [StringLength(500)]
        public string Attachment { get; set; }

        // Meeting
        public DateTime? MeetingStartTime { get; set; }
        public DateTime? MeetingEndTime { get; set; }
        public LMSMeetingLocation? MeetingLocation { get; set; } // Assuming this is a flag for online/offline, adjust if needed

        // Call Details
        public long? CallDuration { get; set; }
        public LMSCallType? CallType { get; set; }
        public long? LeadContactId { get; set; }

        public long? ActivityMasterId { get; set; }

        // Task Details
        public long? TaskOwner { get; set; }
        public long? PositionCode { get; set; }
        public LMSTaskStatus? ActivityStatus { get; set; }
        public LMSTaskPriority? Priority { get; set; }

        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }

        public DateTime? ClosedDateTime { get; set; }
        public string AuditTrail { get; set; }
        // Auditing
        [Required]
        public long CreatedBy { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }
    }
}
