﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.TransactionDb.DbModels
{
    [Table("LMSLeadActivitiesMaster")]
    public class LMSLeadActivityMaster
    {
        [Key]
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long LeadId { get; set; }

        public LMSActivityType ActivityType { get; set; }
        public LMSActivitySource Source { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Attachment { get; set; }
        public string Remark { get; set; }

        public long? LeadContactId { get; set; }

        public long? TaskOwner { get; set; }
        public long? PositionCode { get; set; }
        public LMSTaskStatus? ActivityStatus { get; set; }
        public LMSTaskPriority? Priority { get; set; }
        public DateTime? DueDate { get; set; }
        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public long? UpdatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
