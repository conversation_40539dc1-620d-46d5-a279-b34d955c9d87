﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace Library.NumberSystem;

public enum ColorValue
{
    Red = 1,
    Green = 2,
    Yellow = 3
}

public enum NumberSystems
{
    [Display(Name = "Indian")]
    Indian = 0,

    [Display(Name = "International")]
    International = 1,

    [Display(Name = "French")]
    French = 2,

    [Display(Name = "DotThousand")]
    DotThousand = 3,

    [Display(Name = "ArabicIndic")]
    ArabicIndic = 4,
}

public class ChartColorCodingCommon
{
    public double Defaulters { get; set; }
    public string Measure { get; set; }
    public double Performers { get; set; }
}

public class FormattedData
{
    public FormattedData()
    {
    }

    public FormattedData(string stringVal, double value)
    {
        Value = value;
        StringVal = stringVal;
    }

    public FormattedData(string stringVal, double value, double defaulters, double performers)
    {
        Value = value;
        StringVal = stringVal;
        ColorValue = value <= defaulters ? ColorValue.Red :
            value >= performers ? ColorValue.Green : ColorValue.Yellow;
    }

    public FormattedData(double value)
    {
        Value = value;
    }

    public FormattedData(double value, double defaulters, double performers)
    {
        Value = value;
        ColorValue = value <= defaulters ? ColorValue.Red :
            value >= performers ? ColorValue.Green : ColorValue.Yellow;
    }

    public FormattedData(string stringVal)
    {
        StringVal = stringVal;
        Value = stringVal;
    }

    public string ColorName
    {
        get
        {
            switch (ColorValue)
            {
                case ColorValue.Red:
                    return "#facdcc";
                case ColorValue.Green:
                    return "#dbedd4";
                case ColorValue.Yellow:
                    return "#fef3da";
                default:
                    return "";
            }
        }
    }

    public ColorValue ColorValue { get; set; }
    public string StringVal { get; set; }
    public object Value { get; set; }
}

public class NumberSystem
{
    private readonly NumberSystems numberSystemType;

    public NumberSystem(NumberSystems numberSystemType)
    {
        this.numberSystemType = numberSystemType;
    }

    private FormattedData GetFormattedValueWithColor(double dataValue, ChartColorCodingCommon chartColorCoding)
    {
        var formattedValue = GetFormattedValue(dataValue);
        formattedValue.ColorValue = dataValue <= chartColorCoding.Defaulters ? ColorValue.Red :
            dataValue >= chartColorCoding.Performers ? ColorValue.Green : ColorValue.Yellow;
        return formattedValue;
    }

    public FormattedData FormatData(object val, string measure = null)
    {
        var isDouble = double.TryParse(val.ToString(), out var dataValue);
        var isTime = TimeSpan.TryParse(val.ToString(), out var dataTime);
        if (isDouble)
        {
            var measureLower = !string.IsNullOrEmpty(measure) ? measure.ToLower() : null;
            var isMobileNumber = !string.IsNullOrEmpty(measureLower) && (measureLower.Contains("mobilenumber")
                || measureLower.Contains("distributorphone"));

            var isErpId = !string.IsNullOrEmpty(measureLower) && (measureLower.Contains("distributorerpid")
                || measureLower.Contains("superstockisterpid"));

            if (!string.IsNullOrEmpty(measureLower) && measureLower.Contains("time") && !measureLower.Contains("_"))
            {
                var timeSpan = TimeSpan.FromMinutes(dataValue);
                return new FormattedData(
                    timeSpan.Hours + ":" + timeSpan.Minutes + ":" + timeSpan.Seconds, dataValue);
            }

            if (!string.IsNullOrEmpty(measureLower) && isMobileNumber)
            {
                return new FormattedData(val.ToString());
            }

            if (!string.IsNullOrEmpty(measureLower) && isErpId)
            {
                return new FormattedData(val.ToString());
            }

            return GetFormattedValue(dataValue);
        }

        if (isTime)
        {
            return new FormattedData(val.ToString(), dataTime.TotalMinutes);
        }

        return new FormattedData(val.ToString());
    }

    public static FormattedData FormatCustomTimeData(string strVal)
    {
        var parts = strVal.Split(':');
        if (parts.Length == 2 &&
            int.TryParse(parts[0], out var hours) &&
            int.TryParse(parts[1], out var minutes))
        {
            var timeSpan = new TimeSpan(hours, minutes, 0);
            return new FormattedData(strVal, timeSpan.TotalMinutes);
        }

        return new FormattedData(strVal);
    }

    public FormattedData FormatDataWithColorCoding(object val, ChartColorCodingCommon chartColorCoding,
        string measure = null)
    {
        var isDouble = double.TryParse(val.ToString(), out var dataValue);
        var isTime = TimeSpan.TryParse(val.ToString(), out var dataTime);
        if (isDouble)
        {
            if (measure != null && measure.ToLower().Contains("time"))
            {
                var timeSpan = TimeSpan.FromMinutes(dataValue);
                return new FormattedData(
                    Math.Floor(timeSpan.TotalHours) + ":" + timeSpan.Minutes + ":" + timeSpan.Seconds, dataValue,
                    chartColorCoding.Defaulters, chartColorCoding.Performers);
            }

            return GetFormattedValueWithColor(dataValue, chartColorCoding);
        }

        if (isTime)
        {
            return new FormattedData(val.ToString(), dataTime.TotalMinutes, chartColorCoding.Defaulters,
                chartColorCoding.Performers);
        }

        return new FormattedData(val.ToString());
    }

    public FormattedData GetFormattedValue(double val, bool usesSuperUnitMT = false, bool toKm = false)
    {

        if (usesSuperUnitMT)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000)
            {
                var div = Math.Round(val / 1000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.00")) + " MT";
            }
            else if (val < 1000)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }

        if (toKm)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000)
            {
                var div = Math.Round(val / 1000, 2);
                stringVal = div.ToString("0.##") + " km";
                formattedData.StringVal = stringVal;
                return formattedData;
            }
            else
            {
                stringVal = val.ToString("0.##") + " m";
                formattedData.StringVal = stringVal;
                return formattedData;
            }
        }

        if (numberSystemType == NumberSystems.International)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000000000)
            {
                var div = Math.Round(val / 1000000000.0, 1);
                stringVal = (div == (int)div ? ((int)div).ToString("##,###") : div.ToString("##,###.0")) + " B";
            }
            else if (val >= 1000000)
            {
                var div = Math.Round(val / 1000000.0, 1);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.0")) + " M";
            }
            else if (val >= 1000)
            {
                var div = Math.Round(val, 1);
                stringVal = div == (int)div ? ((int)div).ToString("###,###") : div.ToString("###,###.#");
            }
            else if (val < 1)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }
            else if (val < 1000)
            {
                var div = Math.Round(val, 1);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
        else if (numberSystemType == NumberSystems.French)
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 1000000000)
            {
                var div = Math.Round(val / 1000000000.0, 1);
                stringVal = (div == (int)div ? ((int)div).ToString("N0", CultureInfo.CreateSpecificCulture("fr-FR")) : div.ToString("N1", CultureInfo.CreateSpecificCulture("fr-FR"))) + " Md";
            }
            else if (val >= 1000000)
            {
                var div = Math.Round(val / 1000000.0, 1);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("N1", CultureInfo.CreateSpecificCulture("fr-FR"))) + " M";
            }
            else
            {
                var div = Math.Round(val, 2);
                stringVal = div == (int)div ? ((int)div).ToString("N0", CultureInfo.CreateSpecificCulture("fr-FR")) : div.ToString("N1", CultureInfo.CreateSpecificCulture("fr-FR"));
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
        else if (numberSystemType == NumberSystems.DotThousand)
        {
            var nfi = (NumberFormatInfo)CultureInfo.InvariantCulture.NumberFormat.Clone();
            nfi.NumberGroupSeparator = ".";
            nfi.NumberDecimalSeparator = ",";

            var formattedData = new FormattedData(val);
            string stringVal;

            if (val >= 1_000_000_000)
            {
                var div = Math.Round(val / 1_000_000_000.0, 1);
                stringVal = (div == (int)div
                    ? ((int)div).ToString("N0", nfi)
                    : div.ToString("N1", nfi)) + " B";
            }
            else if (val >= 1_000_000)
            {
                var div = Math.Round(val / 1_000_000.0, 1);
                stringVal = (div == (int)div
                    ? ((int)div).ToString("N0", nfi)
                    : div.ToString("N1", nfi)) + " M";
            }
            else
            {
                var div = Math.Round(val, 2);
                stringVal = div == (int)div
                    ? ((int)div).ToString("N0", nfi)
                    : div.ToString("N2", nfi).TrimEnd('0').TrimEnd(',');
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
        else if (numberSystemType == NumberSystems.ArabicIndic)
        {
            var arabicCulture = new CultureInfo("ar-SA");
            var nfi = (NumberFormatInfo)arabicCulture.NumberFormat.Clone();
            nfi.NumberGroupSeparator = "٬";
            nfi.NumberDecimalSeparator = "٫";

            var formattedData = new FormattedData(val);
            string stringVal;

            if (val >= 1_000_000_000)
            {
                var div = Math.Round(val / 1_000_000_000.0, 1);
                stringVal = (div == (int)div
                    ? ConvertToArabicIndicDigits(((int)div).ToString("N0", nfi))
                    : ConvertToArabicIndicDigits(div.ToString("N1", nfi))) + " مليار";
            }
            else if (val >= 1_000_000)
            {
                var div = Math.Round(val / 1_000_000.0, 1);
                stringVal = (div == (int)div
                    ? ConvertToArabicIndicDigits(((int)div).ToString("N0", nfi))
                    : ConvertToArabicIndicDigits(div.ToString("N1", nfi))) + " مليون";
            }
            else
            {
                var div = Math.Round(val, 2);
                stringVal = div == (int)div
                    ? ConvertToArabicIndicDigits(((int)div).ToString("N0", nfi))
                    : ConvertToArabicIndicDigits(div.ToString("N2", nfi).TrimEnd('0').TrimEnd(nfi.NumberDecimalSeparator[0]));
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
        else
        {
            var stringVal = "";
            var formattedData = new FormattedData(val);
            if (val >= 10000000000)
            {
                var div = Math.Round(val / 10000000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString("##,###") : div.ToString("##,###.0")) + " Cr.";
            }
            else if (val >= 10000000)
            {
                var div = Math.Round(val / 10000000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.00")) + " Cr.";
            }
            else if (val >= 100000)
            {
                var div = Math.Round(val / 100000.0, 2);
                stringVal = (div == (int)div ? ((int)div).ToString() : div.ToString("0.00")) + " Lac";
            }
            else if (val >= 1000)
            {
                var div = val;
                stringVal = div == (int)div ? ((int)div).ToString("##,###") : div.ToString("##,###.#");
            }
            else if (val < 1)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }
            else if (val < 1000)
            {
                var div = Math.Round(val, 2);
                stringVal = (div == (int)div ? (int)div : div).ToString();
            }

            formattedData.StringVal = stringVal;
            return formattedData;
        }
    }

    public static string FormatCurrencyWithComma(decimal value)
    {
        var number = value.ToString("F3");
        var parts = number.Split('.');

        var integerPart = parts[0];
        var fractionalPart = parts[1];

        if (integerPart.Length <= 3)
        {
            return $"{integerPart}.{fractionalPart}";
        }

        var result = "," + integerPart.Substring(integerPart.Length - 3);
        integerPart = integerPart.Substring(0, integerPart.Length - 3);
        while (integerPart.Length > 2)
        {
            result = "," + integerPart.Substring(integerPart.Length - 2) + result;
            integerPart = integerPart.Substring(0, integerPart.Length - 2);
        }

        if (integerPart.Length > 0)
        {
            result = integerPart + result;
        }

        return $"{result}.{fractionalPart}";
    }

    public static string ConvertToArabicIndicDigits(string input)
    {
        return input
            .Replace("0", "٠")
            .Replace("1", "١")
            .Replace("2", "٢")
            .Replace("3", "٣")
            .Replace("4", "٤")
            .Replace("5", "٥")
            .Replace("6", "٦")
            .Replace("7", "٧")
            .Replace("8", "٨")
            .Replace("9", "٩");
    }
}
