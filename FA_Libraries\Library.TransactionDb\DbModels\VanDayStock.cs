﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.TransactionDb.DbModels;

public class VanDayStock
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public long EmployeeId { get; set; }

    public long DistributorId { get; set; }

    public long VanId { get; set; }

    public double CurrentLoad { get; set; }

    public bool isCarryForward { get; set; }

    public List<StockofDayStart> StockofDayStart { get; set; }

    public List<StockofDayEnd> StockofDayEnd { get; set; }

    public DateTime CreatedAt { get; set; }

    public VanLoadOutApprovalStatus? IsApproved { get; set; }

    public DateTime? ApprovedOn { get; set; }

    public long? ApprovedBy { get; set; }

    public bool? IsDayStartStock { get; set; }

    public Guid? SessionId { get; set; }

    public long? PositionCodeId { get; set; }

    public Guid? SettlementID { get; set; }

    public Guid? GUID { get; set; }

    public bool? SettlementStatus { get; set; }

    public VanLoadOutApprovalStatus? IsManagerApproved { get; set; }

    public long? ManagerApprovedBy { get; set; }

    public DateTime? ManagerApprovedOn { get; set; }

    public PortalUserRole? ManagerApprovalRole { get; set; }

    public bool? IsAdditionalStock { get; set; }

    public string RunningSequence { get; set; }

    [Column("WarehouseErpId", TypeName = "nvarchar(64)")]
    public string WarehouseErpId { get; set; }

    [Column("IsFreshStockAdded")]
    public bool IsFreshStockAdded { get; set; }

    public DateTime? LoadOutDate { get; set; }
}
