﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Diagnostics;
using Library.Logs.Services;
using Library.SlackService.Services;

namespace Libraries.Abstractions.Processors
{
    public abstract class TriggeredProcessorBase(FileLogger fileLogger, DirectSlackLogHelper slackLogHelper, string channelId, bool sendCompressedLogs = false)
    {
        protected readonly FileLogger _fileLogger = fileLogger;
        private readonly DirectSlackLogHelper _slackLogHelper = slackLogHelper;
        private readonly string _channelId = channelId;
        private readonly bool _sendCompressedLogs = sendCompressedLogs;
        private readonly Guid _instanceGuid = Guid.NewGuid();
        private string InstanceId => $"{GetType().Name}_{_instanceGuid}";
        /// <summary>
        /// List Slack Ids of users to tag in the error message in format: <@U04ULQ48L93>
        /// </summary>
        protected static readonly List<string> s_slackIdsOfPeopleToAlert = new List<string>();

        protected abstract Task _Process();

        protected virtual string LogHeader => $"[{InstanceId}]: ";
        protected virtual string GetLogFileName()
        {
            return $"{InstanceId}.txt";
        }

        public virtual async Task Process()
        {
            var sw = new Stopwatch();
            sw.Start();
            var logFile = new FileInfo($"{FileLogger.s_logsDirectory}{GetLogFileName()}");

            try
            {
                await _fileLogger.InitializeAsync(logFile.Name, append: false, $"[{GetType().Name}]: ");
                _fileLogger.WriteLine($"Starting processor!");
                await _slackLogHelper.SendMessageToChannel(LogHeader + "\nStarting  processor!", _channelId);
                await _Process();
            }
            catch (Exception ex)
            {
                _fileLogger.WriteLine($"Error while processing:\n{ex}");
                const int StrackTraceMaxLength = 2000;
                var stackTrace = ex.StackTrace != null && ex.StackTrace.Length > StrackTraceMaxLength
                    ? string.Concat("...\n", ex.StackTrace.AsSpan(ex.StackTrace.Length - 1 - StrackTraceMaxLength, StrackTraceMaxLength))
                    : ex.StackTrace;
                await _slackLogHelper.SendMessageToChannel(LogHeader + $"\nUnHandled Exception Occured!\nAlerting:{string.Join(",", s_slackIdsOfPeopleToAlert)}"
                    + $"\n{ex.Message}\n```{stackTrace}```", _channelId);
            }
            finally
            {
                sw.Stop();
                var logMessage = $"Finished processing in {sw.Elapsed.TotalSeconds}.";
                _fileLogger.WriteLine(logMessage);
                _fileLogger.Dispose();
                await _slackLogHelper.SendFileToChannel(logFile, _channelId, compressFile: _sendCompressedLogs);
            }
        }
    }
}
