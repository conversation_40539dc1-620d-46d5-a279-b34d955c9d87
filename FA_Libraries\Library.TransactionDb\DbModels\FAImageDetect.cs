﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.TransactionDb.DbModels;

[Table("FAImageDetects")]
public class FAImageDetect
{
    public long Id { get; set; }

    public Guid? SessionId { get; set; }

    public DateTime CreatedAt { get; set; }

    public long CompanyId { get; set; }

    public long EmployeeId { get; set; }

    [StringLength(1000)]
    public string Remarks { get; set; }

    public Guid? AttendanceId { get; set; }

    public long OutletId { get; set; }

    public string PredictionResult { get; set; }

    [StringLength(1000)]
    public string ImageURLOverall { get; set; }

    [StringLength(1000)]
    public string ImageURL { get; set; }

    public int BrandSkuCount { get; set; } = 0;

    public int TotalObjectCount { get; set; } = 0;

    public double ShelfShare { get; set; } = 0;

    public double ShelfSharePercent { get; set; } = 0;

    public string PredictionResultOverall { get; set; }

    [StringLength(1000)]
    public string OriginalImageURL { get; set; }

    public bool IsProcessed { get; set; } = false;

    public DateTime? ProcessedAt { get; set; }

    public long AssetId { get; set; }

    public long LogicId { get; set; }

    public double OutletScore { get; set; } = 0;

    public double AssetScore { get; set; } = 0;

    public Guid ImageId { get; set; }

    public bool Deleted { get; set; }

    public DateTime? DeletedAt { get; set; }

    public string KPIWeightage { get; set; }

    public string ModelUsed { get; set; }

    public string ObjModelUsed { get; set; }

    public string PremiumSkus { get; set; }

    public ImageState ImageState { get; set; }

    public string MustSellSkus { get; set; }

    public long? EquipmentId { get; set; }
}
