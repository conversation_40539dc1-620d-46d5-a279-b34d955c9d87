﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.Cache;
using Library.CommonServices.CompanySettings.Constants;
using Library.CommonServices.CompanySettings.Repositories;

namespace Library.CommonServices.CompanySettings.Services;

public class CompanySettingsService
{
    private readonly ICompanySettingsRepository _companySettingRepository;
    private readonly CountryInfoService _countryInfoService;
    private readonly CacheHelper? _cacheHelper;

    public CompanySettingsService(ICompanySettingsRepository companySettingRepository, CacheHelper? cacheHelper,
        CountryInfoService countryInfoService)
    {
        _companySettingRepository = companySettingRepository;
        _cacheHelper = cacheHelper;
        _countryInfoService = countryInfoService;
    }

    public async Task<Dictionary<string, object>> GetAllSettingsAsync(long companyId)
    {
        Dictionary<string, object> data;

        if (_cacheHelper != null)
        {
            data = await _cacheHelper.GetResult(CacheKeys.CompanySettingsKey(companyId),
                TimeSpan.FromMinutes(60), async () => await _companySettingRepository.GetAllSettingsAsync(companyId));
        }
        else
        {
            data = await _companySettingRepository.GetAllSettingsAsync(companyId);
        }

        return data;
    }

    private async Task<Dictionary<string, object>> GetSettingsAsync(long companyId, bool onlyApp = false)
    {
        //if (_cacheHelper != null)
        //{
        //    return await _cacheHelper.GetResult(
        //        CacheKeys.GetSettings(companyId, onlyApp), TimeSpan.FromHours(3),
        //        async () => await _companySettingRepository.GetSettingsAsync(companyId, onlyApp));
        //}

        return await _companySettingRepository.GetSettingsAsync(companyId, onlyApp);
    }

    public async Task<CompanySettings> GetCompanySettingsAsync(long companyId, CancellationToken cancellationToken = default)
    {
        var companySettings = await GetSettingsAsync(companyId);
        var data = await _countryInfoService.GetCountryInfoAsync(GetCountryFromSettings(companySettings));
        return new CompanySettings(companySettings, data);
    }

    public async Task<Dictionary<string, object>> GetCompanySettingsV2Async(long companyId, bool onlyApp = false)
    {
        return await _companySettingRepository.GetSettingsV2Async(companyId, onlyApp);
    }

    private static string GetCountryFromSettings(Dictionary<string, object> settings)
    {
        try
        {
            if (settings.ContainsKey("Country"))
            {
                var country = (string)settings["Country"];
                return string.IsNullOrWhiteSpace(country) ? "India" : country;
            }

            return "India";
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine(ex);
            return "India";
        }
    }
}
