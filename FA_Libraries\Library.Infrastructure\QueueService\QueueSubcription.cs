﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using Library.Infrastructure.Constants;

namespace Library.Infrastructure.QueueService;

public enum QueueType
{
    SendEmail,
    SendSMS,
    SlackAutoBug,
    SlackGeneric,
    UpdateEvent,
    UpdateDemoCompany,
    UpdateEventPoison,
    StaleDataTrigger,
    StaleDataTriggerRetry,
    DSRQueue,
    SendEmbeddedEmail,
    SlackCloudLogs,
    SendOtp,
    LiveProcessorEventQueue,
    DeleteUserStatus,
    DeleteMappingsQueueName,
    MTResetSales,
    SendEmailOTP,
    QPSSecondaryQueue,
    EmailDeliveryStatus,
    GcmNotification,
    FcmNotification,
    UpdateDispatchQueueName,
    UpdateDispatchQueueNameNew,
    UpdateNewOutletCountQueueName,
    QueueNameSpecial,
    V4ReportRequestQueue,
    V3ReportRequestQueue,
    V4ReportLiveTransactionReportQueue,
    V4FlexibleRequestQueue,
    V4MasterRequestQueue,
    OutletClusterQueue,
    AssetManagementQueue,
    FloSyncEmployeeQueue,
    MTReportQueue,
    TaskManagementQueue,
    Dsr,
    MonthlyDsr,
    Beatometer,
    TradeAppQueue,
    ETimsManagementQueue,
    LocationAdditionWithCallQueue,
    VanSalesDaySessionQueue,
    FloMasterSyncQueue,
    FloMasterSyncQueueContinous,
    FloCompanyEmployeeSyncQueue,
    FlexibleTargetQueue,
    V4FlexibleTargetAchReportQueue,
    VanSalesDayEndQueue,
    FlexibleReportBuilderQueue,
    FACloneConfigQueue,
    #region RoQueues

    RoEmployeeQueue,
    RoOutletQueue,
    RoOutletClusterQueue,
    RoEmployeeRoutesQueue,
    RoEmployeeRoutesTspQueue,
    RouteIntegrationQueue,

    #endregion,

    #region Route Playground
    RoPlaygroundFileSplitQueue,
    RoPlaygroundAutomaticMasterQueue,
    RoPlaygroundUpdationQueue,
    #endregion

    ApiLogs,

    #region EngageQueues
    EngageLogs,
    EngageMessageNotification,
    #endregion

    #region NewApprovalMechanism

    NewApprovalMechanismTimeline,
    NewApprovalAddUpdateQueue,
    OrderStatusAlertQueue,
    MonetaryBalanceQueue,

    #endregion

    #region ClickHouse Presepectives
    UnifyReturnQueue,
    UnifyPrimaryReprimaryQueue,
    UnifyUserctivityQueue,
    UnifyHistoricalUserctivityQueue,
    UnifyVanSalesQueue,
    #endregion
    #region TargetAutomationQueues

    TsMonthyToDailyQueue,

    #endregion
    RouteOptimizationSplitQueue,
    RouteOptimizationSplitQueueBeta,
    RouteIntegrationTriggeredQueue,
    DJPSequenceQueue,
    DJPSequenceBetaQueue,
    DJPIncrementalQueue,
    DJPIncrementalBetaQueue,
    HCCBCTSApprovalQueue,
    TerritoryOptimizationCreationQueue,
    TerritoryOptimizationUpdationQueue,
    SmbhavOnboardingQueue,
    HccbWeeklyOffUploadQueue,
    NonInvoiceCreateQueue,
    HCCBExportsQueue
}

public class QueueSubcription
{
    private static readonly Dictionary<QueueType, string> queueForEvents = new()
    {
        { QueueType.SendEmail, "email-queue" },
        { QueueType.SendSMS, "sms-queue" },
        { QueueType.SlackAutoBug, "slack-autobug-queue" },
        { QueueType.SlackGeneric, "slack-queue" },
        { QueueType.UpdateEvent, "updateevent-queue" },
        { QueueType.UpdateDemoCompany, "fakeeventqueue" },
        { QueueType.UpdateEventPoison, "updateevent-queue-poison" },
        { QueueType.StaleDataTrigger, "staledata-queue" },
        { QueueType.StaleDataTriggerRetry, "staledata-queue-retry" },
        { QueueType.EmailDeliveryStatus, "email-delivery-status" },
        { QueueType.GcmNotification, "app-notification-queue" },
        { QueueType.FcmNotification, "fcm-notification-queue" },
        { QueueType.SendOtp, "email-queue-otp" },
        { QueueType.LiveProcessorEventQueue, "eventqueue" },
        { QueueType.DeleteUserStatus, "deleteuserstatus" },
        { QueueType.DeleteMappingsQueueName, "mt-deletemapping-queue" },
        { QueueType.MTResetSales, "mtresetsales" },
        { QueueType.SendEmailOTP, "email-queue-otp" },
        { QueueType.QPSSecondaryQueue, "qpsoutletqueue" },
        { QueueType.SendEmbeddedEmail, "email-embedded-queue" },
        { QueueType.SlackCloudLogs, "slack-queue-facloudlogs" },
        { QueueType.DSRQueue, "dsrrequest-queue" },
        { QueueType.V4ReportRequestQueue, "ns-reportrequest-queue" },
        { QueueType.V3ReportRequestQueue, "v3-reportrequest-queue" },
        { QueueType.V4ReportLiveTransactionReportQueue, "ns-reportrequest-live-queue" },
        { QueueType.V4FlexibleRequestQueue, "ns-flexiblereportrequest-queue" },
        { QueueType.V4MasterRequestQueue, "ns-masterreportrequest-queue" },
        { QueueType.OutletClusterQueue, "outletclusterqueue" },
        { QueueType.AssetManagementQueue, "asset-management-queue" },
        { QueueType.UpdateDispatchQueueName, "dispatch-queue" },
        { QueueType.UpdateDispatchQueueNameNew, "dispatch-queue-rp" },
        { QueueType.UpdateNewOutletCountQueueName, "newoutletrequestcount-queue" },
        { QueueType.QueueNameSpecial, "ns-reportrequest-queue-special" },
        { QueueType.FloSyncEmployeeQueue, "wf-employeesync-queue" },
        { QueueType.SmbhavOnboardingQueue, "smbhav-onboarding-queue" },
        { QueueType.MTReportQueue, "mt-reportrequest-queue" },
        { QueueType.TaskManagementQueue, "task-management-queue" },
        { QueueType.Dsr, "ns-dsr-queue" },
        { QueueType.MonthlyDsr, "ns-mdsr-queue" },
        { QueueType.Beatometer, "beatometer-queue" },
        { QueueType.TradeAppQueue, "tradeapp-fcm-notification-queue" },
        { QueueType.ETimsManagementQueue, "etims-management-queue" },
        {QueueType.VanSalesDaySessionQueue, "vansales-daysession-queue" },
        { QueueType.LocationAdditionWithCallQueue, "locationadditionwithcall-eventqueue" },
        { QueueType.FloMasterSyncQueue, "sfatoflomastersync-queue" },
        { QueueType.FloMasterSyncQueueContinous, "sfatoflomastersync-continuous-queue"},
        { QueueType.FloCompanyEmployeeSyncQueue, "wf-companyemployeesync-queue"},
        { QueueType.FlexibleTargetQueue, "flexible-target-queue"},
        { QueueType.V4FlexibleTargetAchReportQueue, "ns-flexibletargetachrequest-queue" },
        { QueueType.FlexibleReportBuilderQueue, "flexible-report-queue" },
        { QueueType.TsMonthyToDailyQueue, "ts-monthytodaily-queue" },
        { QueueType.VanSalesDayEndQueue, "vansales-dayend-queue" },
        { QueueType.FACloneConfigQueue, "fa-cloneconfig-queue"},

        #region RoQueues

        { QueueType.RoEmployeeQueue, "ro-employee-queue" },
        { QueueType.RoOutletQueue, "ro-outlet-queue" },
        { QueueType.RoOutletClusterQueue, "ro-outlet-cluster-queue" },
        { QueueType.RoEmployeeRoutesQueue, "ro-employee-routes-queue" },
        { QueueType.RoEmployeeRoutesTspQueue, "ro-employee-routes-tsp-queue" },
        { QueueType.RouteIntegrationQueue, "route-integration-queue" },
        { QueueType.DJPSequenceBetaQueue, "djp-sequence-beta-queue" },
        { QueueType.DJPSequenceQueue, "djp-sequence-queue" },
        { QueueType.DJPIncrementalBetaQueue, "djp-incremental-beta-queue" },
        { QueueType.DJPIncrementalQueue, "djp-incremental-queue" },
        {QueueType.TerritoryOptimizationCreationQueue, "territory-creation-queue" },
        {QueueType.TerritoryOptimizationUpdationQueue, "territory-updation-queue" },
        #endregion

        #region RoPlaygroundQueues
        { QueueType.RoPlaygroundFileSplitQueue, "ro-playground-file-split-queue"},
        { QueueType.RoPlaygroundAutomaticMasterQueue, "ro-playground-automatic-master-queue"},
        { QueueType.RoPlaygroundUpdationQueue, "ro-playground-updation-queue"},
        #endregion

        { QueueType.ApiLogs, Queues.ApiLogs },

        #region EngageQueues
        { QueueType.EngageLogs, Queues.EngageLogs },
        { QueueType.EngageMessageNotification, Queues.EngageMessageNotification},
        #endregion

        #region NewApprovalMechanism

        {QueueType.NewApprovalMechanismTimeline, Queues.NewApprovalMechanismTimeline},
        {QueueType.NewApprovalAddUpdateQueue, Queues.NewApprovalAddUpdateQueue},
        {QueueType.OrderStatusAlertQueue, Queues.OrderStatusAlertQueue },
        { QueueType.MonetaryBalanceQueue, "monetary-balance-queue" },
        #endregion

        #region ClickHouse Presepectives Queues
        { QueueType.UnifyReturnQueue, "unify-return"},
        { QueueType.UnifyPrimaryReprimaryQueue, "unify-primary-reprimary"},
        { QueueType.UnifyUserctivityQueue, "unify-user-activity"},
        { QueueType.UnifyHistoricalUserctivityQueue, "unify-historical-user-activity"},
        { QueueType.UnifyVanSalesQueue, "unify-van-sales"},
        #endregion
        { QueueType.RouteOptimizationSplitQueue, "ro-split-queue" },
        { QueueType.RouteOptimizationSplitQueueBeta, "ro-split-beta-queue" },
        { QueueType.RouteIntegrationTriggeredQueue, "ro-integration-trigger-queue"},
        { QueueType.HCCBCTSApprovalQueue, "hccb-cts-approval-queue"},
        { QueueType.HccbWeeklyOffUploadQueue, "hccb-weeklyoff-upload-queue" },
        { QueueType.NonInvoiceCreateQueue, "ext-api-create-nonfainvoices" },
        { QueueType.HCCBExportsQueue, "hccb-exports-queue"}
    };

    public QueueSubcription(QueueType queueType)
    {
        Queuestr = queueForEvents[queueType];
        QueueType = queueType;
    }

    public string Queuestr { get; }
    public QueueType QueueType { get; }
}
