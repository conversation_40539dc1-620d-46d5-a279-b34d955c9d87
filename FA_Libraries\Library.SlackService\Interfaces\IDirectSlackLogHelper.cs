﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.IO;
using System.Threading.Tasks;
using SlackNet.WebApi;

namespace Library.SlackService.Interfaces
{
    public interface IDirectSlackLogHelper
    {
        Task<bool> SendFileToChannel(FileInfo logFile, string channelId, bool compressFile = false);
        Task<bool> SendMessage(Message message);
        Task<bool> SendMessageToChannel(string message, string channel, string iconEmoji = ":fa:", string userName = "FA Slack Bot");
    }
}
