﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.RequestApproval
{
    public class RequestApprovalRules
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("RequestType")]
        public ApprovalEngineRequestType RequestType { get; set; }

        [Column("Name")]
        public string Name { get; set; }

        [Column("Description")]
        public string Description { get; set; }

        [Column("ConstraintType")]
        public string ConstraintType { get; set; }

        [Column("Priority")]
        public int Priority { get; set; }

        [Column("ApprovalLevel")]
        public int ApprovalLevel { get; set; }

        [Column("LevelDefinition")]
        public string LevelDefinition { get; set; }

        [Column("IsAdminRequired")]
        public bool? IsAdminRequired { get; set; }

        [Column("IsDeactive")]
        public bool? IsDeactive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }
    }
}
