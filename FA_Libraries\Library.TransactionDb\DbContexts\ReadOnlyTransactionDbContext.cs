﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Library.TransactionDb.DbModels;
using Microsoft.EntityFrameworkCore;

namespace Library.TransactionDb.DbContexts;

public class ReadOnlyTransactionDbContext : DbContext
{
    public ReadOnlyTransactionDbContext(DbContextOptions<ReadOnlyTransactionDbContext> options)
        : base(options)
    {
    }

    public DbSet<FieldEvent> FieldEvents { get; set; }

    public DbSet<DistributorStock> DistributorStocks { get; set; }

    public DbSet<DistributorStockItem> DistributorStockItems { get; set; }

    public DbSet<DayRecord> DayRecords { get; set; }

    public DbSet<DbAttendance> Attendances { get; set; }

    public DbSet<DbSale> Sales { get; set; }

    public DbSet<SchemeSale> SchemeSales { get; set; }

    public DbSet<SchemeSaleItem> SchemeSaleItems { get; set; }

    public DbSet<SurveyResponse> SurveyResponses { get; set; }

    public DbSet<ImageDetect> ImageDetects { get; set; }

    public DbSet<DbSecondarySales> SecondarySales { get; set; }

    public DbSet<DbGameKPIStat> GameKPIStats { get; set; }

    public DbSet<TelecallerOrders> TelecallerOrder { get; set; }

    public DbSet<TelecallerOrderItems> TelecallerOrderItem { get; set; }

    public DbSet<OutletVerification> OutletVerifications { get; set; }

    public DbSet<PrimaryOrders> PrimaryOrders { get; set; }

    public DbSet<PrimaryOrderItems> PrimaryOrderItems { get; set; }

    public DbSet<TADAAlertDetails> TADAAlertDetails { get; set; }

    public DbSet<TADAGSTDurationDetails> TADAGSTDurationDetails { get; set; }

    public DbSet<DeadOutletRequests> DeadOutletRequests { get; set; }

    public DbSet<DeadOutletRequestItems> DeadOutletRequestItems { get; set; }

    public DbSet<DbPrimaryOrderValidation> PrimaryOrderValidation { get; set; }

    public DbSet<DbPrimaryOrderValidationItem> PrimaryOrderValidationItems { get; set; }

    public DbSet<AttendanceRegulariseRequest> AttendanceRegulariseRequest { get; set; }

    public DbSet<DbAssetAllocation> AssetAllocations { get; set; }

    public DbSet<DbAssetAgreement> AssetAgreements { get; set; }

    public DbSet<DAEditRequests> DAEditRequests { get; set; }

    public DbSet<FailedFieldEvent> FailedFieldEvents { get; set; }

    public DbSet<DbRecievedPayment> DbRecievedPayment { get; set; }

    public DbSet<DbAssetReallocationRequests> AssetReallocationRequests { get; set; }

    public DbSet<SecondarySaleItem> SecondarySaleItems { get; set; }

    public DbSet<DistributorStockRequests> DistributorStockRequests { get; set; }

    public DbSet<EInvoicingDetails> EInvoicingDetails { get; set; }

    public DbSet<EmployeeActivities> EmployeeActivities { get; set; }

    public DbSet<EmployeeLeaveRequest> EmployeeLeaveRequests { get; set; }

    public DbSet<OpenMarketDayStock> OpenMarketDayStocks { get; set; }

    public DbSet<OpenMarketDayStart> OpenMarketDayStarts { get; set; }

    public DbSet<OpenMarketDayEnd> OpenMarketDayEnds { get; set; }

    public DbSet<UserCredential> UserCredentials { get; set; }

    public DbSet<SMSRecipientDetails> SMSRecipientDetails { get; set; }

    public DbSet<DbOutletCreationRequests> OutletCreationRequests { get; set; }

    public DbSet<VanSalesInvoiceDetails> VanSalesInvoiceDetails { get; set; }

    public DbSet<VanSaleOutletOutStanding> VanSaleOutletOutStanding { get; set; }

    public DbSet<RetailerCollection> RetailerCollection { get; set; }

    public DbSet<VanSaleItemDetails> VanSaleItemDetails { get; set; }

    // public DbSet<OutletRouteAdditionRequests> OutletRouteAdditionRequests { get; set; }

    public DbSet<UserOTPDetails> UserOTPDetails { get; set; }

    public DbSet<VanDayStock> VanDayStocks { get; set; }

    public DbSet<RetailerReturns> RetailerReturns { get; set; }

    public DbSet<DbVanOrderIntent> VanOrderIntents { get; set; }

    public DbSet<DbVanOrderIntentItem> VanOrderIntentItems { get; set; }

    public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }

    public DbSet<TaskAchievementAndReward> TaskAchievementAndRewards { get; set; }

    public DbSet<MPesaTransactionsRecords> MPesaTransactionsRecords { get; set; }

    public DbSet<FAETimsSalesInvoice> FAETimsSalesInvoice { get; set; }

    public DbSet<PerfectStoreOutletStatus> PerfectStoreOutletStatus { get; set; }

    public DbSet<FocusAreaOutletStatus> FocusAreaOutletStatuses { get; set; }
    public DbSet<ManagerRetailing> ManagerRetailings { get; set; }

    public DbSet<JourneyDiversion> JourneyDiversions { get; set; }

    public DbSet<AssetAudit> AssetAudits { get; set; }

    public DbSet<EventPushConfig> EventPushConfig { get; set; }

    public DbSet<OutletProductRecommendation> OutletProductRecommendations { get; set; }

    public DbSet<SurveyResponseItem> SurveyResponseItems { get; set; }

    public DbSet<EmployeeLocationDump> EmployeeLocationDumps { get; set; }

    public DbSet<OpenMarketPaymentCollection> OpenMarketPaymentCollections { get; set; }

    public DbSet<FAEngageLogWhatsapp> FAEngageLogWhatsapp { get; set; }

    public DbSet<FAManagerEngageLog> FAManagerEngageLogs { get; set; }

    public DbSet<FAEngageLog> FAEngageLogs { get; set; }

    public DbSet<InternationDiscount> InternationDiscounts { get; set; }

    public DbSet<OrderStatus> OrderStatus { get; set; }

    public DbSet<PrimarySchemeSale> PrimarySchemeSales { get; set; }

    public DbSet<PrimarySchemeSaleItem> PrimarySchemeSaleItems { get; set; }

    public DbSet<PaymentsReceived> PaymentsReceived { get; set; }

    public DbSet<OutletCreationRequestManagerEdit> OutletCreationRequestManagerEdits { get; set; }

    public DbSet<EmptyVanStock> EmptyVanStocks { get; set; }

    public DbSet<InvoicePriceEditRequestItems> InvoicePriceEditRequestItems { get; set; }

    public DbSet<InvoicePriceEditRequests> InvoicePriceEditRequests { get; set; }

    public DbSet<StockofDayStart> StockofDayStart { get; set; }

    public DbSet<StockofDayEnd> StockofDayEnd { get; set; }
    public DbSet<LMSLeadActivityMaster> LMSLeadActivitiesMaster { get; set; }

    public DbSet<DayStartTimeLogs> DayStartTimeLogs { get; set; }
    public DbSet<LMSLeadActivityTransactions> LMSLeadActivitiesTransactions { get; set; }

    public override int SaveChanges()
    {
        throw new InvalidOperationException("The context is readonly");
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
    {
        throw new InvalidOperationException("The context is readonly");
    }
}
