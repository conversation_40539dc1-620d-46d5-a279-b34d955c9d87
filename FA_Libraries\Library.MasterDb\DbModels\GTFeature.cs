﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels
{
    [Table("GTFeatures")]
    public class GTFeature
    {
        [Key]
        public long Id { get; set; }
        public string Name { get; set; }
        public GTFeatureEnums FeatureEnum { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public virtual ICollection<GTFeatureMetric> GTFeatureMetrics { get; set; }
    }
}
