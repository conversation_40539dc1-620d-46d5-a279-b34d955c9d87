﻿
using Core.Models.MasterDbModels;
using Core.Repositories;
using HCCB.Core.Models;
using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.DbModel;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class ReuestTimelineRepository : IReuestTimelineRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;
        private readonly WritableMasterDbContext writableMasterDbContext;

        public ReuestTimelineRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext, WritableMasterDbContext writableMasterDbContext)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
            this.writableMasterDbContext = writableMasterDbContext;
        }
        public async Task<List<RequestApprovalTimeline>> GetRequestTimelines(long companyId, long requestId, ApprovalEngineRequestType requestType)
        {
            return await _readOnlyMasterDbContext.RequestApprovalTimelines.Where(e => e.CompanyId == companyId && e.RequestId == requestId && e.RequestType == requestType).ToListAsync();
        }
        public async Task<RequestApprovalTimeline> GetRequestTimelineForStatus(long companyId, long requestId, ApprovalEngineRequestType requestType, ApprovalEngineRequestStatus status)
        {
            return await _readOnlyMasterDbContext.RequestApprovalTimelines.Where(e => e.CompanyId == companyId && e.RequestId == requestId && e.RequestType == requestType && e.RequestStatus == status).FirstOrDefaultAsync();
        }

        public async Task AddRequestToTimeline(RequestApprovalTimeline request)
        {
            await writableMasterDbContext.RequestApprovalTimelines.AddAsync(request);
            await writableMasterDbContext.SaveChangesAsync();
        }

        public async Task UpdateAndApproveRequest(RequestApprovalTimeline request, string status)
        {
            var dbRequest = await writableMasterDbContext.RequestApprovalTimelines
                .Where(e => e.Id == request.Id)
                .FirstOrDefaultAsync();

            dbRequest.RequestStatusString = status;

            var statusHashset = new HashSet<string> { "X", "C", "R", "T", "Z", "A" };
            bool needsApproval = statusHashset.Contains(status);

            if (needsApproval)
            {
                dbRequest.RequestStatus = ApprovalEngineRequestStatus.ExternalFinal;
            }

            await writableMasterDbContext.SaveChangesAsync();
        }
    }
}
