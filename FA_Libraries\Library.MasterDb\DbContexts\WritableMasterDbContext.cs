﻿// Copyright (c) FieldAssist. All Rights Reserved.

using EntityHelper;
using Library.MasterDb.DbModels;
using Microsoft.EntityFrameworkCore;

namespace Library.MasterDb.DbContexts
{
    public class WritableMasterDbContext : Microsoft.EntityFrameworkCore.DbContext
    {
        public WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options)
            : base(options)
        {
        }

        public DbSet<LocationDB> F2KLocations { get; set; }

        public DbSet<PinCodeMaster> PinCodeMaster { get; set; }

        public DbSet<OutletUpdationRequest> FAOutletUpdationRequests { get; set; }

        public DbSet<AssetOutletMapping> AssetOutletMappings { get; set; }

        public DbSet<EquipmentMaster> EquipmentMaster { get; set; }

        public DbSet<AssetTypes> AssetTypes { get; set; }

        public DbSet<EmployeeAdvanceLeave> EmployeeAdvanceLeaves { get; set; }

        public DbSet<AdvanceLeaveSubmission> AdvanceLeaveSubmissions { get; set; }

        public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }

        public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }

        public DbSet<RoutePlanRequest> RoutePlanRequests { get; set; }

        public DbSet<RoutePlanRequestItem> RoutePlanRequestItems { get; set; }

        public DbSet<EmployeeDailyTargets> EmployeeDailyTargets { get; set; }

        public DbSet<EmployeeDailyTargetItems> EmployeeDailyTargetItems { get; set; }

        public DbSet<ManagerAlerts> ManagerAlerts { get; set; }

        public DbSet<DeviceConnection> DeviceConnections { get; set; }

        public DbSet<DeviceToken> DeviceTokens { get; set; }

        public DbSet<Device> Devices { get; set; }

        public DbSet<Device_New> Devices_New { get; set; }

        public DbSet<EmployeeToken> ClientEmployeeTokens { get; set; }

        public DbSet<EmployeeRouteDiversion> EmployeeRouteDiversions { get; set; }

        public DbSet<WritableRouteOutletMapping> RouteOutletMappings { get; set; }

        public DbSet<CueCardsMaster> CueCardsMaster { get; set; }

        public DbSet<OutletMetric> OutletMetric { get; set; }

        public DbSet<OutletwiseExternalMetricValues> OutletwiseExternalMetricValues { get; set; }

        public DbSet<DanoneRunningNumbers> DanoneRunningNumbers { get; set; }

        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }

        public DbSet<Distributor> Distributors { get; set; }

        public DbSet<DistributorBeatMappingDB> DistributorBeatMappings { get; set; }

        public DbSet<Employee> Employees { get; set; }

        public DbSet<CompanyFactoryStocks> CompanyFactoryStocks { get; set; }

        public DbSet<SchemeBasket> SchemeBuckets { get; set; }

        public DbSet<SchemeSlab> SchemeSlabs { get; set; }

        public DbSet<ProductTagMaster> ProductTagMasters { get; set; }

        public DbSet<ProductTagSuggestion> ProductTagSuggestions { get; set; }

        public DbSet<EngageNotification> FaEngageNotificationMasters { get; set; }

        public DbSet<NotificationMessage> FaEngageNotificationMessages { get; set; }

        public DbSet<ReportUseDetails> ReportUseDetails { get; set; }

        public virtual DbSet<EmailSchedule> EmailSchedules { get; set; }

        public DbSet<DumpReportRequest> DumpReportRequests { get; set; }

        public DbSet<AnalyticsReportRequest> AnalyticsReportRequests { get; set; }

        public DbSet<ReportSubscription> ReportSubscriptions { get; set; }

        public DbSet<FocusedProductTarget> FocusedProductTargets { get; set; }

        public DbSet<GameIncentive> GameIncentives { get; set; }

        public DbSet<GameIncentiveSlab> GameIncentiveSlabs { get; set; }

        public DbSet<AssortedProductRule> AssortedProductRules { get; set; }

        public DbSet<ShopTypesForAssortedProductRule> ShopTypesForAssortedProductRules { get; set; }

        public DbSet<SamplingProduct> FASamplingProductSales { get; set; }

        public DbSet<FAProductRecommendationLogic> FAProductRecommendationLogics { get; set; }

        public DbSet<FAProductWinBackLogic> FAProductWinBackLogics { get; set; }

        public DbSet<SegmentationForAssortedProductRule> SegmentationForAssortedProductRules { get; set; }

        public DbSet<AssortedProductSKUDetails> AssortedProductSKUDetails { get; set; }

        public DbSet<AttendanceNormPolicy> AttendanceNormPolicies { get; set; }

        public DbSet<BeatometerRule> BeatometerRule { get; set; }

        public DbSet<CFRetailerInvoiceData> CFRetailerInvoiceDatas { get; set; }

        public DbSet<FaEngageKPITrigger> FaEngageKPITriggers { get; set; }

        public DbSet<DerivedKPI> DerivedKPIs { get; set; }

        public DbSet<UserUIPreference> UserUIPreferences { get; set; }

        public DbSet<OutletCreationRequest> OutletCreationRequests { get; set; }

        public DbSet<Company> Companies { get; set; }

        public DbSet<ChartVizDetails> ChartVizDetails { get; set; }

        public DbSet<QueryView> QueryViews { get; set; }

        public DbSet<OutletReachRule> OutletReachRules { get; set; }

        public DbSet<ProductBatchMaster> ProductBatches { get; set; }

        public DbSet<EmployeeTargetV2> EmployeeTargetV2 { get; set; }

        public DbSet<CustomReport> CustomReports { get; set; }

        public DbSet<CustomReportItem> CustomReportItems { get; set; }

        public DbSet<UserWiseCustomReport> UserWiseCustomReports { get; set; }

        public DbSet<UserWiseCustomReportItem> UserWiseCustomReportItems { get; set; }

        public DbSet<Geographies> Geographies { get; set; }

        public DbSet<PmsRule> PmsRules { get; set; }

        public DbSet<PmsRuleMetricConstraint> PmsRuleMetricConstraints { get; set; }

        public DbSet<AreaSalesManager> AreaSalesManagers { get; set; }

        public DbSet<RegionalSalesManager> RegionalSalesManagers { get; set; }

        public DbSet<ZonalSalesManager> ZonalSalesManager { get; set; }

        public DbSet<NationalSalesManager> NationalSalesManager { get; set; }

        public DbSet<GlobalSalesManager> GlobalSalesManager { get; set; }

        public DbSet<ConcurrentRequest> ConcurrentRequests { get; set; }

        public DbSet<ISROutletMapping> ISROutletMappings { get; set; }

        public DbSet<ManagerAppPointerMappings> ManagerAppPointerMappings { get; set; }

        public DbSet<KRATargets> KRATargets { get; set; }

        public DbSet<KRAMapping> KRAMappings { get; set; }

        public DbSet<Holidays> Holidays { get; set; }

        public DbSet<E11SuggestiveOrder> E11SuggestiveOrders { get; set; }

        public DbSet<CompanyKRATarget> CompanyKRATargets { get; set; }

        public DbSet<FACompanyModulesMapping> FACompanyModulesMapping { get; set; }

        public DbSet<EmployeeTourPlanItemSecondary> EmployeeTourPlanItemsSecondary { get; set; }
        public DbSet<ApprovalRequest> ApprovalRequests { get; set; }

        public DbSet<UserTrainingProgress> UserTrainingProgress { get; set; }
        public DbSet<CompanyTargetSubscriptions> CompanyTargetSubscriptions { get; set; }
        public DbSet<TargetMaster> TargetMaster { get; set; }
        public DbSet<IDSLogins.IdsLogin> IDSLogins { get; set; }
        public DbSet<LMSAccount> LMSAccounts { get; set; }
        public DbSet<LMSAccountContact> LMSAccountContacts { get; set; }
        public DbSet<LMSAccountAddress> LMSAccountAddresses { get; set; }
        public DbSet<LMSAccountTemplate> LMSAccountTemplates { get; set; }
        public DbSet<LMSAccountNote> LMSAccountNotes { get; set; }
        public DbSet<LMSCompanyLeadStage> LMSCompanyLeadStages { get; set; }
        public DbSet<LMSCompanyLeadTemplate> LMSCompanyLeadTemplates { get; set; }
        public DbSet<LMSGlobalLeadStage> LMSGlobalLeadStages { get; set; }
        public DbSet<LMSGlobalLeadTemplate> LMSGlobalLeadTemplates { get; set; }
        public DbSet<LMSLead> LMSLeads { get; set; }
        public DbSet<LMSLeadContact> LMSLeadContacts { get; set; }
        public DbSet<LMSLeadSource> LMSLeadSources { get; set; }
        public DbSet<LMSCustomField> LMSCustomFields { get; set; }
        public DbSet<LMSCustomFieldValue> LMSCustomFieldValues { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DeviceToken>().HasIndex(p => p.EmployeeId).IsUnique();

            modelBuilder.Entity<LocationDB>()
             .Property(sample => sample.Latitude)
             .HasColumnType("decimal(18,10)");

            modelBuilder.Entity<LocationDB>()
             .Property(sample => sample.Longitude)
             .HasColumnType("decimal(18,10)");

            modelBuilder.Entity<Distributor>()
             .Property(sample => sample.Longitude)
             .HasColumnType("decimal(18,10)");

            modelBuilder.Entity<Distributor>()
             .Property(sample => sample.Latitude)
             .HasColumnType("decimal(18,10)");

            modelBuilder.Entity<LocationDB>().ToTable(t => t.HasTrigger("F2KLocationsAfterUpdate"));
            modelBuilder.Entity<Distributor>().ToTable("FADistributors", t => t.HasTrigger("FADistributorsAfterUpdate"));
        }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("use save changes async instead");
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            var newCreatedEntities = ChangeTracker.Entries<ICreatedEntity>()
               .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
            var now = DateTime.UtcNow;
            foreach (var item in newCreatedEntities)
            {
                item.CreatedAt = now;
                item.CreationContext = "From gt app";
            }

            var newDeviceEntities = ChangeTracker.Entries<IDeviceEntity>()
               .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
            foreach (var item in newDeviceEntities)
            {
                item.ServerTime = now;
            }

            var newUpdatedEntities = ChangeTracker.Entries<IUpdatedEntity>().Where(e => e.State == EntityState.Modified).Select(e => e.Entity).ToList();
            foreach (var item in newUpdatedEntities)
            {
                item.LastUpdatedAt = now;
            }

            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
