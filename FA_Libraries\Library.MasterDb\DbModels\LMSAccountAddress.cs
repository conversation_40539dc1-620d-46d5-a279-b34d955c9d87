﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("LMSAccountAddresses")]
    public class LMSAccountAddress
    {
        [Key]
        public long Id { get; set; }
        [ForeignKey("LMSAccount")]
        public long AccountId { get; set; }
        public virtual LMSAccount LMSAccount { get; set; }

        public long CompanyId { get; set; }

        [Required]
        [StringLength(255)]
        public string AddressName { get; set; }

        [StringLength(255)]
        public string Street { get; set; }

        [StringLength(100)]
        public string City { get; set; }

        [StringLength(50)]
        public string State { get; set; }

        [StringLength(20)]
        public string PinCode { get; set; }

        [StringLength(50)]
        public string Country { get; set; }

        [Column(TypeName = "decimal(10, 6)")]
        public decimal? Latitude { get; set; }

        [Column(TypeName = "decimal(10, 6)")]
        public decimal? Longitude { get; set; }
        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
