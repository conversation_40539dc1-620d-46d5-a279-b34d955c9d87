﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Libraries.PerspectiveColumns.Interface;

namespace Libraries.PerspectiveColumns;

public class EmpGeoPerformanceModel : IPerspective
{
    private readonly LinkNames linkNames;

    public EmpGeoPerformanceModel(Dictionary<string, string> nomenclatureDict)
    {
        linkNames = LinkNames.GetLinkNames(nomenclatureDict);
    }

    List<PerspectiveColumnModel> IPerspective.Columns { get => Columns; set => throw new NotImplementedException(); }

    public List<PerspectiveColumnModel> Columns => new List<PerspectiveColumnModel>
    {
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.GlobalSalesManager            , Name = "GSM"      , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.NationalSalesManager          , Name = "NSM"      , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.ZonalSalesManager             , Name = "ZSM"      , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.RegionalSalesManager          , Name = "RSM"      , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.AreaSalesManager              , Name = "ASM"      , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.Employee                      , Name = "ESM"      , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.GlobalSalesManager +" ErpId"  , Name = "GSMErpId" , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.NationalSalesManager +" ErpId", Name = "NSMErpId" , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.ZonalSalesManager +" ErpId"   , Name = "ZSMErpId" , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.RegionalSalesManager +" ErpId", Name = "RSMErpId" , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.AreaSalesManager +" ErpId"    , Name = "ASMErpId" , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Field User" , DisplayName = linkNames.Employee +" ErpId"            , Name = "ESMErpId" , IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false},
        // Name Property Of New Positions need to be in this Pattern (LXPosition) only or Filtering based on setting won't work
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L8Position                   , Name = "L8Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L8Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L8Position + " Code"         , Name = "L8Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L8Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L8Position + " User"         , Name = "L8Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L8Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L8Position + " User ErpId"   , Name = "L8Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L8Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L7Position                   , Name = "L7Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L7Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L7Position + " Code"         , Name = "L7Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L7Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L7Position + " User"         , Name = "L7Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L7Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L7Position + " User ErpId"   , Name = "L7Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L7Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L6Position                   , Name = "L6Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L6Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L6Position + " Code"         , Name = "L6Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L6Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L6Position + " User"         , Name = "L6Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L6Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L6Position + " User ErpId"   , Name = "L6Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L6Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L5Position                   , Name = "L5Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L5Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L5Position + " Code"         , Name = "L5Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L5Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L5Position + " User"         , Name = "L5Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L5Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L5Position + " User ErpId"   , Name = "L5Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L5Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L4Position                   , Name = "L4Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L4Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L4Position + " Code"         , Name = "L4Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L4Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L4Position + " User"         , Name = "L4Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L4Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L4Position + " User ErpId"   , Name = "L4Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L4Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L3Position                   , Name = "L3Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L3Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L3Position + " Code"         , Name = "L3Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L3Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L3Position + " User"         , Name = "L3Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L3Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L3Position + " User ErpId"   , Name = "L3Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L3Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L2Position                   , Name = "L2Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L2Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L2Position + " Code"         , Name = "L2Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L2Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L2Position + " User"         , Name = "L2Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L2Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L2Position + " User ErpId"   , Name = "L2Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L2Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L1Position                   , Name = "L1Position"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L1Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L1Position + " Code"         , Name = "L1Position_Code"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L1Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L1Position + " User"         , Name = "L1Position_User"            , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L1Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L1Position + " User ErpId"   , Name = "L1Position_UserErpId"       , IsMeasure = false , IsDimension = true , SubGroup = linkNames.L1Position},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = "Employee " + linkNames.AttributeText1 , Name = "EmployeeAttributeText1", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = "Employee " + linkNames.AttributeText2 , Name = "EmployeeAttributeText2", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = linkNames.L1Position + "HQ" , Name = "UserHQ", IsMeasure = false, IsDimension = true},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = "Employee Product Division" , Name = "EmpProductDivision", IsMeasure = false, IsDimension = true},
        new PerspectiveColumnModel { Attribute = "Position" , DisplayName = "Employee " + linkNames.AttributeNumber1 , Name = "EmployeeAttributeNumber1", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Position" , DisplayName = "Employee " + linkNames.AttributeNumber2 , Name = "EmployeeAttributeNumber2", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Position" , DisplayName = "Date Of Joining" , Name = "DateOfJoining", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Position" , DisplayName = "Email Id" , Name = "EmailId", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Position" , DisplayName = "User Type" , Name = "UserType", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Position" , DisplayName = "Contact Number" , Name = "ContactNumber", IsMeasure = false, IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Position"        , DisplayName = "Designation"                     , Name = "Designation" , IsMeasure = false , IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown},
        new PerspectiveColumnModel { Attribute = "Time" , DisplayName = "Date" , Name = "Date", IsDimension = true, PerspectiveMeasure = PerspectiveMeasure.Unknown },
        //new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = linkNames.Level7 , Name = "Level7" , IsMeasure = false , IsDimension = true , IsPivot = false , IsOtherMeasure = false },
        //new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = linkNames.Level6 , Name = "Level6" , IsMeasure = false , IsDimension = true , IsPivot = false , IsOtherMeasure = false },
        //new PerspectiveColumnModel() { Attribute = "Sales Territory" , DisplayName = linkNames.Level5 , Name = "Level5" , IsMeasure = false , IsDimension = true , IsPivot = false , IsOtherMeasure = false },
        new PerspectiveColumnModel { Attribute = "Sales Territory" , DisplayName = linkNames.Zone , Name = "Zone", IsMeasure = false, IsDimension = true, IsPivot = false, IsOtherMeasure = false },
        new PerspectiveColumnModel { Attribute = "Sales Territory" , DisplayName = linkNames.Region , Name = "Region", IsMeasure = false, IsDimension = true, IsPivot = false , IsOtherMeasure = false},
        new PerspectiveColumnModel { Attribute = "Master Measure"  , DisplayName = "Employee Overall Target"          , Name = "Targets"                  , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
        //Date: 05-09-2024 ;Asana: https://app.asana.com/0/1205462988686818/1208016603254324/f;
        //change: fix total outlet count for quick viz chart
        new PerspectiveColumnModel { Attribute = "Master Measure"  , DisplayName = "USC (Route-Master)"                      , Name = "USC_Route"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Master Measure"  , DisplayName = "Total Beats"                      , Name = "Beats"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Master Measure"  , DisplayName = "Total Outlets"                    , Name = "Outlets"                 , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Avg },
        new PerspectiveColumnModel { Attribute = "Master Measure"  , DisplayName = "Total Routes"                      , Name = "Routes"                   , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "New Outlet Count(Without Approval)"    , Name = "NewOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "New Outlet Count(With Approval)"           , Name = "NewOutletCount" , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "Total Focused Outlets"             , Name = "TotalFocusedOutlets"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "Distributors"                     , Name = "Distributors"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "New outlets activation"             , Name = "NewOutletsActivation"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "New outlets activation Value"      , Name = "NewOutletsActivationValue"        , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "Focused Outlet Target"             , Name = "FocusedOutletTarget"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Master Measure"   , DisplayName = "UBO Target"                        , Name = "UBOTarget"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Value"                            , Name = "Value"                    , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Sum   },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "NetValue"                         , Name = "NetValue"                 , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Sum   },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Order Qty ({linkNames.StdUnit})" , Name = "OrderQtyInStdUnit"        , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Sum   },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Order Qty ({linkNames.Unit})"    , Name = "OrderInUnits"             , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Sum   },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "TC"                               , Name = "TC"                       , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "PC"                               , Name = "PC"                       , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Avg. TC (per day)"                , Name = "AvgTCPerday"              , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Avg. PC (per day)"                , Name = "AvgPCPerday"              , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "LPC"                              , Name = "LPC"                      , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Avg Retailing Time"               , Name = "AvgRetailingTime"         , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value , PerspectiveType = PerspectiveType.DayStart , DataType = RangeType.TimeSpan},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Physical Retail Time"             , Name = "PhysicalRetailTime"       , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value , DataType = RangeType.TimeSpan},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Avg Sales (per day)"              , Name = "AvgValuePerDay"                 , IsMeasure = true  , IsDimension = false   , PerspectiveMeasure = PerspectiveMeasure.Value , PerspectiveType = PerspectiveType.DayStart ,},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = linkNames.UTC                      , Name = "UTC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = linkNames.UPC                      , Name = "UPC"                     , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value     },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Days Retailing"                   , Name = "DaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Actual Days Retailing"                   , Name = "ActualDaysRetailing"           , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Count  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Scheduled Days"                   , Name = "ScheduledDays"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Loss of Mandays"                  , Name = "LossOfMandays"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Retailer Stock Calls"             , Name = "RetailerStockCalls"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Days Started Late"                  , Name = "DaysStartedLate"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Count  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Days Frst Call OVC"                  , Name = "DaysFrstCallOVC"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Count  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = linkNames.OVT                      , Name = "OVT"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "OVC"                              , Name = "OVC"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Order Qty ({linkNames.SuperUnit})"             , Name = "OrderQtyInSupUnit"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Focus Product Order Qty ({linkNames.Unit})"     , Name = "FPOrderQtyUnit"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Focus Product Order Qty ({linkNames.StdUnit})"  , Name = "FPOrderQtyStdUnit"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Focus Product Order Qty ({linkNames.SuperUnit})"  , Name = "FPOrderQtySupUnit"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Focus Product Order Value"  , Name = "FPOrderValue"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Net Value (Dispatch)"                , Name = "DispatchInRevenue"              , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Dispatch Qty  ({linkNames.SuperUnit})"              , Name = "DispatchInSupUnits"              , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Dispatch Qty ({linkNames.StdUnit})"              , Name = "DispatchInStdUnits"              , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Dispatch Qty ({linkNames.Unit})"                 , Name = "DispatchInUnits"              , IsMeasure = true , IsDimension = true , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = linkNames.AvgValuePerCall , Name = "AvgValuePerCall"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Total Lines"                              , Name = "TotalLinesSold"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        //new PerspectiveColumnModel() { Attribute = "Sales Measure"   , DisplayName = "Total Unique Lines"                              , Name = "TotalUniqueLinesSold"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "SC"                               , Name = "SC"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "USC"                              , Name = "USC"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "New Outlets Value"                , Name = "NewOutletsValue"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Value per New Outlet"             , Name = "ValuePerNewOutlet"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"FO Qty ({linkNames.Unit})"       , Name = "FOQty"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"FO Qty ({linkNames.StdUnit})"    , Name = "FOStdQty"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"FO Qty ({linkNames.SuperUnit})"  , Name = "FOSuperQty"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "FO Value"             , Name = "FOValue"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Focused Outlets "+ linkNames.UTC   , Name = "FocusedOutletsUTC"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Focused Outlets "+ linkNames.UPC   , Name = "FocusedOutletsUPC"              , IsMeasure = true , IsDimension = false , PerspectiveMeasure = PerspectiveMeasure.Value  },
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Retailer Return Value" , Name = "RetailerReturnInRevenue", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Retailer Return Qty ({linkNames.Unit})" , Name = "RetailerReturnInUnits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Retailer Return Qty ({linkNames.StdUnit})" , Name = "RetailerReturnInStdUnits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = $"Retailer Return Qty ({linkNames.SuperUnit})" , Name = "RetailerReturnInSuperUnits", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Count Of Official Work" , Name = "CountOfOfficialWork", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
        new PerspectiveColumnModel { Attribute = "Sales Measure"   , DisplayName = "Official Work Time" , Name = "OfficialWorkHours", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Sum},
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "Avg First Call Time", Name = "AvgFirstCallTime", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, PerspectiveType = PerspectiveType.DayStart, DataType = RangeType.TimeSpan },
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "Avg Day Start Time", Name = "AvgDayStartTime", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, PerspectiveType = PerspectiveType.DayStart, DataType = RangeType.TimeSpan },
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "Total Retail Time in Market", Name = "TotalRetailTimeInMarket", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, PerspectiveType = PerspectiveType.DayStart, DataType = RangeType.TimeSpan },
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "Avg Time Spent at an Outlet", Name = "AvgTimeSpentAtAnOutlet", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, PerspectiveType = PerspectiveType.Attendance, DataType = RangeType.TimeSpan },
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "Total Time", Name = "TotalTime", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, PerspectiveType = PerspectiveType.DayStart, DataType = RangeType.TimeSpan },
        new PerspectiveColumnModel { Attribute = "Sales Measure", DisplayName = "Average Total Time", Name = "AvgTotalTime", IsMeasure = true, IsDimension = false, PerspectiveMeasure = PerspectiveMeasure.Value, PerspectiveType = PerspectiveType.DayStart, DataType = RangeType.TimeSpan },

    };
}
