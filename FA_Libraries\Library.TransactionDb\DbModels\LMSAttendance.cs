﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.TransactionDb.DbModels
{
    [Table("LMSAttendances")]
    public class LMSAttendance
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public int CompanyId { get; set; }

        [Required]
        public long EmployeeId { get; set; }

        public DateTimeOffset? DeviceTime { get; set; }

        public DateTimeOffset? ServerTime { get; set; }

        public DateTime? CreatedAt { get; set; }

        public long? FAEventId { get; set; }

        public double? GeoTaggingDistance { get; set; }

        [Required]
        public Guid Guid { get; set; }

        [Required]
        public int ActivityType { get; set; }

        public long? LocationId { get; set; }

        public bool? IsOVC { get; set; }

        public bool? IsGPSOff { get; set; }

        public Guid? SessionId { get; set; }

        public long? PositionCodeId { get; set; }

        public DateTime? ActivityStartTime { get; set; }

        public DateTime? ActivityEndTime { get; set; }

        public int? DurationMinutes { get; set; }

        public bool? IsTelephonic { get; set; }

        public bool? IsProductiveEffective { get; set; }

        public bool? IsReviewed { get; set; }

        public bool? IsValid { get; set; }

        public string NoActivityReason { get; set; }

        public string FAUnifySource { get; set; }

        public long? FAUnifyUserId { get; set; }

        public FieldEvent FAEvent { get; set; }
    }
}
