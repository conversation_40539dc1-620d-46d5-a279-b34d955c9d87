﻿// Copyright (c) FieldAssist. All Rights Reserved.

using SlackNet.WebApi;
using SlackNet;
using System.IO.Compression;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.IO;
using System;
using System.Net.Http.Headers;
using System.Linq;
using Library.SlackService.Interfaces;

namespace Library.SlackService.Services
{
    public class SlackBotService : ISlackBotService
    {
        private readonly HttpClient _httpClient;
        private readonly ISlackApiClient _slackClient;

        public SlackBotService(IHttpClientFactory httpClientFactory, ISlackApiClient slackClient)
        {
            _httpClient = httpClientFactory.CreateClient();
            _slackClient = slackClient;
        }

        public async Task JoinChannel(string channelId)
        {
            await _slackClient.Conversations.Join(channelId);
        }

        public async Task SendFileToChannel(FileInfo logFile, string channelId, bool compressFile = false)
        {
            if (compressFile)
            {
                await CompressFileAndUpload(logFile, channelId);
            }
            else
            {
                await UploadFie(logFile, channelId);
            }
        }

        public async Task SendMessage(Message message)
        {
            await _slackClient.Chat.PostMessage(message);
        }

        public async Task SendMessageToChannel(string message, string channel, string iconEmoji = ":fa:", string userName = "FA Slack Bot")
        {
            await SendMessage(new Message
            {
                Text = message,
                Channel = channel,
                IconEmoji = iconEmoji,
                Username = userName,
            });
        }

        private async Task<ExternalFileReference> CompressFileAndUpload(FileInfo logFile, string channelId)
        {
            // Compress the log file
            var compressedLogFile = new FileInfo($"{logFile.FullName}.gz");
            using (var originalFileStream = logFile.OpenRead())
            {
                using (var compressedFileStream = new FileStream(compressedLogFile.FullName, FileMode.Create, FileAccess.Write))
                {
                    using (var compressionStream = new GZipStream(compressedFileStream, CompressionMode.Compress))
                    {
                        await originalFileStream.CopyToAsync(compressionStream);
                    }
                }
            }

            var fileUploadResponse = await UploadFie(compressedLogFile, channelId);

            // Clean up the compressed file
            if (compressedLogFile.Exists)
            {
                compressedLogFile.Delete();
            }

            return fileUploadResponse;
        }

        private async Task<ExternalFileReference> UploadFie(FileInfo logFile, string channelId)
        {
            var uploadURL = await _slackClient.Files.GetUploadUrlExternal(logFile.Name, (int)logFile.Length);
            await UploadFileToURL(uploadURL.UploadUrl, logFile);
            return (await _slackClient.Files.CompleteUploadExternal(
                new List<ExternalFileReference> { new ExternalFileReference { Id = uploadURL.FileId, Title = logFile.Name } }, channelId)).Single();
        }

        private async Task<HttpResponseMessage> UploadFileToURL(string uploadUrl, FileInfo file)
        {
            using var fileStream = file.OpenRead();
            using var content = new MultipartFormDataContent();

            using var fileContent = new StreamContent(fileStream);
            fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("text/plain");

            content.Add(fileContent, "file", Path.GetFileName(file.Name));

            var response = await _httpClient.PostAsync(uploadUrl, content);

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("File uploaded successfully.");
            }
            else
            {
                Console.WriteLine($"Upload failed. Status code: {response.StatusCode}");
            }

            return response;
        }
    }
}
