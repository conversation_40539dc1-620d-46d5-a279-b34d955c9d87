﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("DistributorWiseTaxIntegration")]
    public class DistributorWiseTaxIntegration
    {
        [Key]
        public long Id { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? LastUpdatedAt { get; set; }

        [Required]
        [StringLength(64)]
        public string CreationContext { get; set; }

        public long CompanyId { get; set; }

        public long? DistributorId { get; set; }

        [Required]
        [Column(TypeName = "nvarchar(max)")]
        public string ConfigJson { get; set; }

        [ForeignKey(nameof(TaxIntegrationDevice))]
        public long TaxIntegrationDeviceId { get; set; }

        public bool IsDeleted { get; set; }

        // Navigation property
        public virtual TaxIntegrationDevice TaxIntegrationDevice { get; set; }
    }
}
