﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums
{
    public enum ApprovalEngineRequestType
    {
        OutletCreation = 1,
        OutletUpdation = 2,
        AssetAllocation = 3,
        AssetReallocation = 4,
        DistributorStock = 5,
        PrimaryOrder = 6,
        AssetAgreement = 7,
        Loyalty = 8
    }

    public enum ApprovalRequestType
    {
        Outlet = 1,
        Asset = 2,
        PrimaryOrder = 3,
        DistributorStock = 4
    }

    public enum ApprovalEngineRequestStatus
    {
        [Display(Name = "Pending")]
        Pending = 0,
        [Display(Name = "Approved")]
        Approved = 1,
        [Display(Name = "Rejected")]
        Rejected = 2,
        [Display(Name = "Awaiting At Other Level")]
        AwaitingOnOtherLevel = 3,
        [Display(Name = "Rejected By Other Level")]
        RejectedByOtherLevel = 4,
        [Display(Name = "Auto Approved")]
        AutoApproved = 5,
        [Display(Name = "Approved By Admin")]
        ApprovedByAdmin = 6,
        [Display(Name = "Rejected By Admin")]
        RejectedByAdmin = 7,
        [Display(Name = "Pending At Processor")]
        PendingAtProcessor = 8,  // To maintain data processing at Approval Engine

        // >=1000 enums are maintaining external statuses of Requests in case Enterprise clients use external approval Systems

        [Display(Name = "External Pending")]
        ExternalPending = 1000,
        [Display(Name = "External Final")]
        ExternalFinal = 1001
    }
}
