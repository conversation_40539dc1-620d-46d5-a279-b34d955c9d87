﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FA_Libraries\EntityHelper\EntityHelper.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.CommonHelpers\Library.CommonHelpers.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.EmailService\Library.EmailService.csproj" />
  </ItemGroup>

</Project>
