﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace Library.MasterDb.DbModels;

public class LocationMin
{
    public long Id { get; set; }

    public string ShopName { get; set; }

    public string ErpId { get; set; }

    public long? BeatId { get; set; }

    public string Beat { get; set; }

    public bool IsBlocked { get; set; }

    public decimal? Latitude { get; set; }

    public decimal? Longitude { get; set; }

    public VerificationStatus VerificationStatus { get; set; }

    public List<long> CustomTags { get; set; }
}
