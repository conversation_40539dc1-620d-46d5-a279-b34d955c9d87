﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("LMSGlobalLeadTemplate")]
    public class LMSGlobalLeadTemplate
    {
        [Key]
        public long Id { get; set; }

        public long CompanyId { get; set; }

        [Required]
        [StringLength(255)]
        public string TemplateName { get; set; }

        [Required]
        public string TemplateContent { get; set; } // NVARCHAR(MAX)

        public LMSTemplateType TemplateType { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }

    public enum LMSTemplateType
    {
        Email = 1,
        SMS = 2,
        WhatsApp = 3
    }
}
