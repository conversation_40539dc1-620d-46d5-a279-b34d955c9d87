﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.MasterDb.DbModels
{
    [Table("LMSLeadContacts")]
    public class LMSLeadContact
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [ForeignKey("LMSLead")]
        public long LeadId { get; set; }
        public virtual LMSLead LMSLead { get; set; }
        public long CompanyId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        [StringLength(20)]
        public string MobileNumber { get; set; }

        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; }

        [Column(TypeName = "date")]
        public DateTime? DateOfBirth { get; set; }

        [StringLength(100)]
        public string Designation { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public string Photo { get; set; }

        public bool IsDecisionMaker { get; set; }

        public bool IsDeleted { get; set; }

        public long CreatedBy { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public long? UpdatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }
    }
}
