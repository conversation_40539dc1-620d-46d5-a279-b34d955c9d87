﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Library.Infrastructure.Interface;
using Library.SlackService.Model;
using Library.StorageWriter.Reader_Writer;

namespace Library.EmailService.Interface
{
    public interface IEmailMessage : IQueueMessage
    {
        string Bcc { get; set; }
        string Cc { get; set; }
        string FromEmail { get; set; }
        string FromName { get; set; }
        bool IsHTML { get; set; }
        string Message { get; set; }
        string Subject { get; set; }
        string To { get; set; }
    }

    public class EmailMessage : IEmailMessage
    {
        public string Bcc { get; set; }
        public string Cc { get; set; }
        public string ContentPath { get; set; }
        public string FromEmail { get; set; }
        public string FromName { get; set; }
        public bool IsHTML { get; set; }
        public string Message { get; set; }
        public string Subject { get; set; }
        public string To { get; set; }

        public async Task VerifyContent(string masterStorageConnectionString)
        {
            if ((Message?.Length ?? 0) > 40000)
            {
                var blobWriter = new AnonymousBlobWriter(masterStorageConnectionString);
                var filepath = $"{Guid.NewGuid()}.html";
                Message = await blobWriter.WriteToBlob(filepath, Message, "text/html");
                ContentPath = Message;
            }
        }

        public ISlackMessage ToSlackMessage(string channel)
        {
            return new SlackMessage
            {
                Channel = channel,
                Text = $"Email:{Subject}",
                Username = "DebugEmailRequest",
                Attachments = new List<Attachment>
                {
                    new Attachment
                    {
                        Title="EmailDetails",
                        Fields=new List<Field>
                        {
                            new Field{Title="From",Value=$"[{FromName}]{FromEmail}",Short=true},
                            new Field{Title="To",Value=To,Short=true},
                            new Field{Title="Subject",Value=Subject,Short=false},
                            new Field{Title="BCC",Value=Bcc,Short=true},
                            new Field{Title="IsHtml",Value=IsHTML.ToString(),Short=true},
                            new Field{Title="Body",Value=Message,Short=false}
                        }
                    }
                }
            };
        }
    }
}
