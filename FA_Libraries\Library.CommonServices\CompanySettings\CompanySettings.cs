﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;
using Libraries.CommonEnums.Helpers;
using Library.CommonServices.CompanySettings.Models;
using Library.MasterDb.DbModels;
using Library.NumberSystem;
using Library.StringHelpers;
using Newtonsoft.Json.Linq;

namespace Library.CommonServices.CompanySettings
{
    public class CompanySettings : ICompanySettings
    {
        protected readonly Dictionary<string, object> _settings;
        protected readonly CountryInfo? _countryInfo;

        public CompanySettings(Dictionary<string, object> companySettingsDict, CountryInfo? countryInfo = null)
        {
            _settings = companySettingsDict;
            _countryInfo = countryInfo;
        }

        private CountryInfo GetCountryInfo()
        {
            if (_countryInfo == null)
            {
                throw new NullReferenceException("Country Info is null. Did you forget to initialize it?");
            }

            return _countryInfo!;
        }

        public virtual TimeSpan TimeZoneOffset => TimeSpan.FromMinutes(GetCountryInfo().TimeZoneOffsetMinutes);

        public string Country
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("Country"))
                    {
                        var country = (string)_settings["Country"];
                        return string.IsNullOrWhiteSpace(country) ? "India" : country;
                    }
                    else
                    {
                        return "India";
                    }
                }
                catch (Exception)
                {
                    return "India";
                }
            }
        }

        public JourneyPlanType GetJourneyPlanType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("JourneyPlanType"))
                    {
                        var journeyPlanType = (string)_settings["JourneyPlanType"];
                        return journeyPlanType.TestNullAssign(JourneyPlanType.Default);
                    }
                    else
                    {
                        return JourneyPlanType.Default;
                    }
                }
                catch (Exception)
                {
                    return JourneyPlanType.Default;
                }
            }
        }

        public bool UsesReverseGeocodes
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("usesReverseGeocodes") && (bool)_settings["usesReverseGeocodes"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesCustomSequenceNumbers
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesCustomSequenceNumbers"))
                    {
                        return (bool)_settings["CompanyUsesCustomSequenceNumbers"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesCustomerERPIntegrationForDeliveryApp
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesCustomerERPIntegrationForDeliveryApp"))
                    {
                        return (bool)_settings["CompanyUsesCustomerERPIntegrationForDeliveryApp"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool ShopAdditionRequest
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("CompanyUsesAddOutletRequest") &&
                           (bool)_settings["CompanyUsesAddOutletRequest"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public long GetNewShopSurveyId
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("NewShopSurvey") ? (long)_settings["NewShopSurvey"] : 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public long GetWFHSurveyId
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("WFHSurveyId") ? (long)_settings["WFHSurveyId"] : 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public bool CanMapPDtoDist
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("usesDistProdDivBeatMappings") &&
                           (bool)_settings["usesDistProdDivBeatMappings"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public double MapInvalidDistanceLimit
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("MapInvalidDistanceLimit")
                        ? (double)_settings["MapInvalidDistanceLimit"]
                        : 200;
                }
                catch (Exception)
                {
                    return 200;
                }
            }
        }

        public bool IsUsingBeatPlan => GetJourneyPlanType == JourneyPlanType.BeatPlan;

        public bool CalculateOutletLocationAttributes =>
            CalculateOutletLocationAttributesType != CalculateOutletLocationAttributesType.None;

        public CalculateOutletLocationAttributesType CalculateOutletLocationAttributesType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CalculateOutletLocationAttributesType"))
                    {
                        var journeyPlanType = (string)_settings["CalculateOutletLocationAttributesType"];
                        return journeyPlanType.TestNullAssign(CalculateOutletLocationAttributesType
                            .CalculateAtNewOutlets);
                    }
                    else
                    {
                        return CalculateOutletLocationAttributesType.CalculateAtNewOutlets;
                    }
                }
                catch (Exception)
                {
                    return CalculateOutletLocationAttributesType.CalculateAtNewOutlets;
                }
            }
        }

        public bool UsesNsApp
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("UserUsesNSApp") && (bool)_settings["UserUsesNSApp"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesCouponBasedLoyaltyScheme
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("CompanyUsesCouponBasedLoyaltyScheme") &&
                           (bool)_settings["CompanyUsesCouponBasedLoyaltyScheme"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesOpenMarketOperations
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("CompanyUsesOpenMarketOperations") &&
                           (bool)_settings["CompanyUsesOpenMarketOperations"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool UsesPositionCodes
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("UsesPositionCodes") && (bool)_settings["UsesPositionCodes"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool ShowSelfBeatRoute
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("ShowSelfBeatRoute") && (bool)_settings["ShowSelfBeatRoute"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool IsOnlineDMS
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("IsOnlineDMS") && (bool)_settings["IsOnlineDMS"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool IsSyncSo
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("DMSSyncSO") && (bool)_settings["DMSSyncSO"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool IsSyncPo
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("DMSPOSync") && (bool)_settings["DMSPOSync"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool NotifyManagerWhenDistributorStockIsTaken
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("NotifyManagerWhenDistributorStockIsTaken")
                           && (bool)_settings["NotifyManagerWhenDistributorStockIsTaken"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool UsesAutomaticERPIDWithRegionCode
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("AutomaticERPIDWithRegionCode") &&
                           (bool)_settings["AutomaticERPIDWithRegionCode"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public string UsesOutletVerification
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UsesOutletVerification"))
                    {
                        var mappingType = (string)_settings["UsesOutletVerification"];
                        return mappingType;
                    }
                    else
                    {
                        return "Not Applicable";
                    }
                }
                catch (Exception)
                {
                    return "Not Applicable";
                }
            }
        }

        public bool AutoApproveOutletVerificationRequest
        {
            get
            {
                try
                {
                    return _settings.ContainsKey("AutoApproveOutletVerificationRequest") &&
                           (bool)_settings["AutoApproveOutletVerificationRequest"];
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public string NewOutletApprovalLevel
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("NewOutletApprovalLevel"))
                    {
                        return (string)_settings["NewOutletApprovalLevel"];
                    }
                    else
                    {
                        return "ASM";
                    }
                }
                catch (Exception)
                {
                    return "ASM";
                }
            }
        }

        public bool UsesDistProdDivBeatMappings
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("usesDistProdDivBeatMappings"))
                    {
                        return (bool)_settings["usesDistProdDivBeatMappings"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool UserAllowedToBookOrderWithoutDistributor
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UserAllowedToBookOrderWithoutDistributor"))
                    {
                        return (bool)_settings["UserAllowedToBookOrderWithoutDistributor"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyAllowsOutletERPIDGenerationUsingCustomLogic
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyAllowsOutletERPIDGenerationUsingCustomLogic"))
                    {
                        return (bool)_settings["CompanyAllowsOutletERPIDGenerationUsingCustomLogic"];
                    }
                    else
                    {
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine(ex);
                    return false;
                }
            }
        }

        public int NoOfDigits => GetCountryInfo().DigitsInPhNo;

        public string CountryCode => GetCountryInfo().CountryName;

        public virtual string CurrencySymbol => GetCountryInfo().CurrencySymbol;

        public int TaxIDLength => GetCountryInfo().TaxNumberLength;

        public int taxType => GetCountryInfo().taxType;

        public CountryInfo CountryInfo => GetCountryInfo();

        public bool UsesBeatPlans => GetJourneyPlanType == JourneyPlanType.BeatPlan;

        public bool UsesPJP => PJPFrequencyType != JourneyFrequency.Unknown;

        public JourneyFrequency PJPFrequencyType
        {
            get
            {
                switch (GetJourneyPlanType)
                {
                    case JourneyPlanType.PJPFourWeekly:
                        return JourneyFrequency.FourWeeks;
                    case JourneyPlanType.PJPOpen:
                        return JourneyFrequency.Open;
                    default:
                        return JourneyFrequency.Unknown;
                }
            }
        }

        public List<string> GetOfficialWorkTypes
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("OfficialWorkType"))
                    {
                        if (_settings["OfficialWorkType"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["OfficialWorkType"];
                        }
                        else if (_settings["OfficialWorkType"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["OfficialWorkType"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> GetCustomPaymentOptions
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CustomPaymentOptions"))
                    {
                        if (_settings["CustomPaymentOptions"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["CustomPaymentOptions"];
                        }
                        else if (_settings["CustomPaymentOptions"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["CustomPaymentOptions"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> CompanyWantsDDSRToSelectThePartialDeliveryReasons
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyWantsDDSRToSelectThePartialDeliveryReasons"))
                    {
                        if (_settings["CompanyWantsDDSRToSelectThePartialDeliveryReasons"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["CompanyWantsDDSRToSelectThePartialDeliveryReasons"];
                        }
                        else if (_settings["CompanyWantsDDSRToSelectThePartialDeliveryReasons"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["CompanyWantsDDSRToSelectThePartialDeliveryReasons"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        // public TimeSpan TimeZoneOffset
        // {
        //    get
        //    {
        //        try
        //        {
        //            if (settings.ContainsKey("TimeZoneOffset"))
        //            {
        //                return TimeSpan.FromMinutes((int)(long)settings["TimeZoneOffset"]);
        //            }
        //            else
        //                return TimeSpan.FromMinutes(330);
        //        }
        //        catch (Exception)
        //        {
        //            return TimeSpan.FromMinutes(330);
        //        }
        //    }
        // }

        public bool CompanyDoesNotShowNonSaleableProductsOnLoadout =>
            GetBooleanSettingValue("CompanyDoesNotShowNonSaleableProductsOnLoadout");

        public bool CompanyCreatesOutletWithoutOrderOrRequest =>
            GetBooleanSettingValue("CompanyCreatesOutletWithoutOrderOrRequest");

        public bool CompanyUsesPreTaxOrPostTaxDiscount => GetBooleanSettingValue("CompanyUsesPreTaxOrPostTaxDiscount");

        public bool SchemeOnMasterandManufacturingBatch =>
            GetBooleanSettingValue("SchemeOnMasterandManufacturingBatch");

        public bool CompanyUsesExciseDutyOnProducts => GetBooleanSettingValue("CompanyUsesExciseDutyOnProducts");

        public bool CompanyUsesMasterBatch => GetBooleanSettingValue("CompanyUsesMasterBatch");

        public bool CompanyUsesCustomerCategoryWisePricing
            => GetBooleanSettingValue("CompanyUsesCustomerCategoryWisePricing");

        public bool CompanyAllowsDistributorWiseLoadApproval =>
            GetBooleanSettingValue("CompanyAllowsDistributorWiseLoadApproval");

        public bool CompanyAllowsDSRToCollectPaymentForPreSales =>
            GetBooleanSettingValue("CompanyAllowsDSRToCollectPaymentForPreSales");

        public bool Companydoesnotallowtocreatenewoutletsinpresale =>
            GetBooleanSettingValue("Companydoesnotallowtocreatenewoutletsinpresale");

        public bool CompanyAllowsBatchManagementForVanSales =>
            GetBooleanSettingValue("CompanyAllowsBatchManagementForVanSales");

        public bool CompanyAllowsReplacementAgainstDifferentProduct =>
            GetBooleanSettingValue("CompanyAllowsReplacementAgainstDifferentProduct");

        public bool CompanySendsPricingInPrimaryInvoiceAPI => GetBooleanSettingValue("CompanySendsPricingInPrimaryInvoiceAPI");

        public string CompanyAllowsPromotorToTakeInward
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyAllowsPromotorToTakeInward"))
                    {
                        return (string)_settings["CompanyAllowsPromotorToTakeInward"];
                    }

                    return "Standalone";
                }
                catch (Exception)
                {
                    return "Standalone";
                }
            }
        }

        public bool CompanyUsesLoadoutLoadinAutomation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesLoadoutLoadinAutomation"))
                    {
                        return (bool)_settings["CompanyUsesLoadoutLoadinAutomation"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesKYCVerification => GetBooleanSettingValue("CompanyUsesKYCVerification");

        public bool CompanyUsesLoadOutOrderIntent => GetBooleanSettingValue("CompanyUsesLoadOutOrderIntent");

        public bool HideWarehouseStock => GetBooleanSettingValue("HideWarehouseStock");

        public bool CompanyUsesDistributorSKUMapping => GetBooleanSettingValue("CompanyUsesDistributorSKUMapping");

        public bool SecondaryTarget => GetBooleanSettingValue("usesEmployeeTarget");

        public string PrimaryTarget
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("PrimaryTargetsOn"))
                    {
                        return (string)_settings["PrimaryTargetsOn"];
                    }
                    else
                    {
                        return "No Targets";
                    }
                }
                catch (Exception)
                {
                    return "No Targets";
                }
            }
        }

        public bool AutomaticCombinationOfCityGrades =>
            GetBooleanSettingValue("AutomaticCombinationOfCityGrades");

        public bool CompanyUsesMaximumStockNorm => GetBooleanSettingValue("Companyusesmaximumstocknorm");

        public bool CompanyUsesRouteOptimization => GetBooleanSettingValue("CompanyUsesRouteOptimization");

        public string CompanyUsesCreditLimit
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesCreditLimit"))
                    {
                        return (string)_settings["CompanyUsesCreditLimit"];
                    }
                    else
                    {
                        return "No Validation";
                    }
                }
                catch (Exception)
                {
                    return "No Validation";
                }
            }
        }

        public bool UsesDistributorsOfRegion => GetBooleanSettingValue("usesDistributorsofRegion");

        public List<string> GetCompanyShopTypes
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("shopTypes"))
                    {
                        if (_settings["shopTypes"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["shopTypes"];
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public int MonthStartDate
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("monthStartDate"))
                    {
                        return (int)(long)_settings["monthStartDate"];
                    }
                    else
                    {
                        return 1;
                    }
                }
                catch (Exception)
                {
                    return 1;
                }
            }
        }

        public int MaximumDiscountForAnOrder
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MaximumDiscountForAnOrder"))
                    {
                        return (int)(long)_settings["MaximumDiscountForAnOrder"];
                    }
                    else
                    {
                        return 0;
                    }
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public int DaysUptoWhichEnablePrevMonthRegularization
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DaysUptoWhichEnablePrevMonthRegularization"))
                    {
                        return (int)(long)_settings["DaysUptoWhichEnablePrevMonthRegularization"];
                    }
                    else
                    {
                        return 0;
                    }
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public bool IsUsingSuggestiveQuantity => GetBooleanSettingValue("CompanyUsesSuggestiveQuantity");

        public bool AllowReturnQuantityGreaterThanOrderQuantity =>
            GetBooleanSettingValue("AllowReturnQuantityGreaterThanOrderQuantity");

        public bool ShowNoStockProductsForVanSales => GetBooleanSettingValue("ShowNoStockProductsForVanSales");

        public bool CompanyAllowsSkipOTPVerificationInODS =>
            GetBooleanSettingValue("CompanyAllowsSkipOTPVerificationInODS");

        public bool CompanyUsesStockNorm => GetBooleanSettingValue("CompanyUsesStockNorm");

        public bool Mandatorilycaptureinvoiceduringvalidation =>
            GetBooleanSettingValue("Mandatorilycaptureinvoiceduringvalidation");

        public bool UsesAssetAuditing => GetBooleanSettingValue("UsesAssetAuditing");

        public bool UsesRegionalAdminApprovalForSubDistributor =>
            GetBooleanSettingValue("UsesRegionalAdminApprovalForSubDistributor");

        public bool CompanyDoesNotUseGST => GetBooleanSettingValue("CompanyDoesNotUseGST");

        public bool IsUsingPendingOrders => GetBooleanSettingValue("usesPendingOrders");

        public int YearStartMonth
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("yearStartMonth"))
                    {
                        return (int)(long)_settings["yearStartMonth"];
                    }
                    else
                    {
                        return 4;
                    }
                }
                catch (Exception)
                {
                    return 4;
                }
            }
        }

        public bool ShouldReverseGeocodeDayStarts => GetBooleanSettingValue("usesReverseGeocodes");

        public bool IsUsingCallPrepStatsCalculation => GetBooleanSettingValue("CalculateCallPrepScreenData");

        public bool UsesProductRegionalPricing => GetBooleanSettingValue("usesRegionalPricing");

        public bool CompanyUsesSkipODSFlow => GetBooleanSettingValue("CompanyUsesSkipODSFlow");

        public TypeofDistributorMapping TypeofDistributorMapping
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("TypeofDistributorMapping"))
                    {
                        var mappingType = (string)_settings["TypeofDistributorMapping"];
                        return mappingType.TestNullAssign(TypeofDistributorMapping.BeatDistributor);
                    }
                    else
                    {
                        return TypeofDistributorMapping.BeatDistributor;
                    }
                }
                catch (Exception)
                {
                    return TypeofDistributorMapping.BeatDistributor;
                }
            }
        }

        public bool UsesSalesInAmount => GetBooleanSettingValue("UsesSalesinQuantity");

        public bool UsesCloseToExpiry => GetBooleanSettingValue("usesCloseToExpiry");

        public bool UsesConversionFactor => GetBooleanSettingValue("UsesConversionFactor");

        public bool UsesRetailerStock => GetBooleanSettingValue("IsUsesRetailerStock");

        public bool PurchasePriceInclusiveOfTax => GetBooleanSettingValue("PurchasePriceInclusiveOfTax");

        public string DiscountType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DiscountType"))
                    {
                        return (string)_settings["DiscountType"];
                    }
                    else
                    {
                        return "Default";
                    }
                }
                catch (Exception)
                {
                    return "Default";
                }
            }
        }

        public bool UsesDistributorCashDiscount => GetBooleanSettingValue("DistributorCashDiscount");

        public List<string> GetUsesMustSellReason
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForMustSellProduct"))
                    {
                        if (_settings["ReasonForMustSellProduct"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonForMustSellProduct"];
                        }
                        else if (_settings["ReasonForMustSellProduct"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonForMustSellProduct"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public string ProductRecommendationRanking
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ProductRecommendationRanking"))
                    {
                        return (string)_settings["ProductRecommendationRanking"];
                    }
                    else
                    {
                        return "Tag specific ranking";
                    }
                }
                catch (Exception)
                {
                    return "Tag specific ranking";
                }
            }
        }

        public List<string> GetCityGrades
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CityGrades"))
                    {
                        if (_settings["CityGrades"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["CityGrades"];
                        }
                        else if (_settings["CityGrades"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["CityGrades"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> GetReasonForNotTakingRetailerStock
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForNotTakingRetailerStock"))
                    {
                        if (_settings["ReasonForNotTakingRetailerStock"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonForNotTakingRetailerStock"];
                        }
                        else if (_settings["ReasonForNotTakingRetailerStock"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonForNotTakingRetailerStock"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> GetReasonForProductReturn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForProductReturn"))
                    {
                        if (_settings["ReasonForProductReturn"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonForProductReturn"];
                        }
                        else if (_settings["ReasonForProductReturn"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonForProductReturn"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public int NoOfDaysForMarkingDeadOutlet
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("NoOfDaysForMarkingDeadOutlet"))
                    {
                        return (int)(long)_settings["NoOfDaysForMarkingDeadOutlet"];
                    }
                    else
                    {
                        return 365;
                    }
                }
                catch (Exception)
                {
                    return 365;
                }
            }
        }

        public bool TelephonicOrdersAvailable => GetBooleanSettingValue("telephonicOrdersAvailable");

        public int MaxOutletInBeatOrRoute
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MaxOutletInBeatOrRoute"))
                    {
                        return (int)(long)_settings["MaxOutletInBeatOrRoute"];
                    }
                    else
                    {
                        return 1000;
                    }
                }
                catch (Exception)
                {
                    return 1000;
                }
            }
        }

        public bool CompanyUsesAddOutletRequest => GetBooleanSettingValue("CompanyUsesAddOutletRequest");

        public string MinimumLevelOfApprovalForAttendanceRegularization
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MinimumLevelOfApprovalForAttendanceRegularization"))
                    {
                        return (string)_settings["MinimumLevelOfApprovalForAttendanceRegularization"];
                    }
                    else
                    {
                        return "ASM";
                    }
                }
                catch (Exception)
                {
                    return "ASM";
                }
            }
        }

        public bool ShowShopAdditionScreen => GetBooleanSettingValue("ShowShopAdditionScreen");

        public bool NewOutletCreationByOTP => GetBooleanSettingValue("NewOutletCreationByOTP");

        public bool BlockOrderBookingIfNotPhysicallyPresent =>
            GetBooleanSettingValue("BlockOrderBookingIfNotPhysicallyPresent");

        public int DayLimitforOrderDispatch
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DayLimitforOrderDispatch"))
                    {
                        return (int)(long)_settings["DayLimitforOrderDispatch"];
                    }
                    else
                    {
                        return 15;
                    }
                }
                catch (Exception)
                {
                    return 15;
                }
            }
        }

        public List<string> GetReasonForJourneyDiversion
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("RouteDiversion"))
                    {
                        if (_settings["RouteDiversion"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["RouteDiversion"];
                        }
                        else if (_settings["RouteDiversion"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["RouteDiversion"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> NoAssetAuditReasons
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("NoAssetAuditReasons"))
                    {
                        if (_settings["NoAssetAuditReasons"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["NoAssetAuditReasons"];
                        }
                        else if (_settings["NoAssetAuditReasons"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["NoAssetAuditReasons"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> VanLoadinDiscrepencyRemarks
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("VanLoadinDiscrepencyRemarks"))
                    {
                        if (_settings["VanLoadinDiscrepencyRemarks"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["VanLoadinDiscrepencyRemarks"];
                        }
                        else if (_settings["VanLoadinDiscrepencyRemarks"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["VanLoadinDiscrepencyRemarks"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public bool IsUsesOutletMargin => GetBooleanSettingValue("outletMargin");

        public bool IsOrderReviewAllowed => GetBooleanSettingValue("callReviewAllowed");

        public bool UsesTabularWhatsAppOrderFormat => GetBooleanSettingValue("CompanyUsesTabularWhatsAppOrderFormat");

        public string DistributorStockType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DistributorStockType"))
                    {
                        return (string)_settings["DistributorStockType"];
                    }
                    else
                    {
                        return "Default";
                    }
                }
                catch (Exception)
                {
                    return "Default";
                }
            }
        }

        public int MinimumDaysForTourPlan
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MinimumDaysForTourPlan"))
                    {
                        return (int)(long)_settings["MinimumDaysForTourPlan"];
                    }
                    else
                    {
                        return 15;
                    }
                }
                catch (Exception)
                {
                    return 15;
                }
            }
        }

        public int DayLimitForPrimaryOrderDispatch
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DayLimitForPrimaryOrderDispatch"))
                    {
                        return (int)(long)_settings["DayLimitForPrimaryOrderDispatch"];
                    }
                    else
                    {
                        return 0;
                    }
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public bool UsesIntelligentSchemes => GetBooleanSettingValue("usesIntelligentSchemes");

        public bool UsesSecondarySales => GetBooleanSettingValue("usesSecondarySales");

        public bool UsesPrimaryOrder => GetBooleanSettingValue("usesPrimaryOrder");

        public bool CallReviewAllowedForPrimaryOrder => GetBooleanSettingValue("callReviewAllowedForPrimaryOrder");

        public EmployeeTargetOn EmployeeTargetType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("EmployeeTargetType"))
                    {
                        var employeeTargetType = (string)_settings["EmployeeTargetType"];
                        return employeeTargetType.TestNullAssign(EmployeeTargetOn.None);
                    }
                    else
                    {
                        return EmployeeTargetOn.None;
                    }
                }
                catch (Exception)
                {
                    return EmployeeTargetOn.None;
                }
            }
        }

        public TargetValueTypeDashboard TargetValueTypeDashboard
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("TargetValueType"))
                    {
                        var employeeTargetType = (string)_settings["TargetValueType"];
                        return employeeTargetType.TestNullAssign(TargetValueTypeDashboard.Revenue);
                    }
                    else
                    {
                        return TargetValueTypeDashboard.Revenue;
                    }
                }
                catch (Exception)
                {
                    return TargetValueTypeDashboard.Revenue;
                }
            }
        }

        public string CompanyPricingModel
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("companyPricingModel"))
                    {
                        return _settings["companyPricingModel"].ToString();
                    }
                    else
                    {
                        return "Default";
                    }
                }
                catch (Exception)
                {
                    return "Default";
                }
            }
        }

        public string CompanyPricingType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("companyPricingType"))
                    {
                        return _settings["companyPricingType"].ToString();
                    }
                    else
                    {
                        return "Flat";
                    }
                }
                catch (Exception)
                {
                    return "Flat";
                }
            }
        }

        public EmployeeTargetsCalculationType CalculateAchievementAgainstEmployeeTargets
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CalculateAchievementAgainstEmployeeTargets"))
                    {
                        var calculationType = (string)_settings["CalculateAchievementAgainstEmployeeTargets"];
                        return calculationType.TestNullAssign(EmployeeTargetsCalculationType.Order);
                    }
                    else
                    {
                        return EmployeeTargetsCalculationType.Order;
                    }
                }
                catch (Exception)
                {
                    return EmployeeTargetsCalculationType.Order;
                }
            }
        }

        public bool DuplicateOutletCheckOnline => GetBooleanSettingValue("DuplicateOutletCheckOnline");

        public bool EnableExternalAssets => GetBooleanSettingValue("enableExternalAssets");

        public bool CompanyUsesOutletReach => GetBooleanSettingValue("CompanyUsesOutletReach");

        public bool CompanyUsesPreviousLoadoutAutofill => GetBooleanSettingValue("CompanyUsesPreviousLoadoutAutofill");

        public bool UsesFocusProductTarget => GetBooleanSettingValue("UsesFocusProductTarget");

        public bool IsCompanyUsesTwentyMinutesAddress =>
            GetBooleanSettingValue("IsCompanyUsesAddressForJourneyPlotting");

        public OrderBookingScreenType AppOrderBookingScreenType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("AppOrderBookingScreenType"))
                    {
                        var calculationType = (string)_settings["AppOrderBookingScreenType"];
                        return calculationType.TestNullAssign(OrderBookingScreenType.Main);
                    }
                    else
                    {
                        return OrderBookingScreenType.Main;
                    }
                }
                catch (Exception)
                {
                    return OrderBookingScreenType.Main;
                }
            }
        }

        public string FABattleGround
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("FABattleGround"))
                    {
                        return (string)_settings["FABattleGround"];
                    }
                    else
                    {
                        return "Not Applicable";
                    }
                }
                catch (Exception)
                {
                    return "Not Applicable";
                }
            }
        }

        public DuplicateOutletCheck DuplicateOutletCheck
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DuplicateOutletCheck"))
                    {
                        var employeeTargetType = (string)_settings["DuplicateOutletCheck"];
                        return employeeTargetType.TestNullAssign(DuplicateOutletCheck.NotApplicable);
                    }
                    else
                    {
                        return DuplicateOutletCheck.NotApplicable;
                    }
                }
                catch (Exception)
                {
                    return DuplicateOutletCheck.NotApplicable;
                }
            }
        }

        // todo: extract app settings from settings
        // public Dictionary<string, object> GetAppSettingDictionary()
        // {
        //     return appSettings;
        // }
        public JArray FAFloIntegrationSettings
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("FAFloIntegrationSettings"))
                    {
                        var configData = (string)_settings["FAFloIntegrationSettings"];
                        return JArray.Parse(configData);
                    }
                    else
                    {
                        return new JArray();
                    }
                }
                catch (Exception)
                {
                    return new JArray();
                }
            }
        }

        public bool IsUsingLMS => GetBooleanSettingValue("isUsingLMS");
        public bool CompanyUsesLMS => GetBooleanSettingValue("CompanyUsesLMS");
        public bool UsesIOSApp => GetBooleanSettingValue("CompanyUsesIOSApp");

        public bool UsesTADA => GetBooleanSettingValue("usesTADA");

        public bool UsesFAFLO => GetBooleanSettingValue("usesFaFlo");

        public bool UsesVanSales => GetBooleanSettingValue("usesVanSales");

        public bool CompanyUsesDMS => GetBooleanSettingValue("CompanyUsesDMS");

        public bool IsPrintingAvailable => GetBooleanSettingValue("IsPrintingAvailable");

        public bool CompanyUsesCESS => GetBooleanSettingValue("CompanyUsesCESS");

        public TypeofTaxCalculation TypeOfTaxCalculation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("TypeofTaxCalculation"))
                    {
                        var mappingType = (string)_settings["TypeofTaxCalculation"];
                        return mappingType.TestNullAssign(TypeofTaxCalculation.Default);
                    }
                    else
                    {
                        return TypeofTaxCalculation.Default;
                    }
                }
                catch (Exception)
                {
                    return TypeofTaxCalculation.Default;
                }
            }
        }

        public TargetOn TargetOn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("EmployeeTargetType"))
                    {
                        var employeeTargetType = (string)_settings["TargetOn"];
                        return employeeTargetType.TestNullAssign(TargetOn.OverAll);
                    }
                    else
                    {
                        return TargetOn.OverAll;
                    }
                }
                catch (Exception)
                {
                    return TargetOn.OverAll;
                }
            }
        }

        public TargetOn BeatTargetOn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("Beatwisetargeton"))
                    {
                        var employeeTargetType = (string)_settings["Beatwisetargeton"];
                        return employeeTargetType.TestNullAssign(TargetOn.OverAll);
                    }
                    else
                    {
                        return TargetOn.OverAll;
                    }
                }
                catch (Exception)
                {
                    return TargetOn.OverAll;
                }
            }
        }

        public bool UsesPaymentFeature => GetBooleanSettingValue("usesPaymentFeature");

        public bool CompanyUsesRegionWiseOutletWiseVerification =>
            GetBooleanSettingValue("CompanyUsesRegionWiseOutletWiseVerification");

        public double GSTTaxCalculationPercentage
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("GSTTaxCalculationPercentage"))
                    {
                        return (double)_settings["GSTTaxCalculationPercentage"];
                    }
                    else
                    {
                        return 0.00;
                    }
                }
                catch (Exception)
                {
                    return 0.00;
                }
            }
        }

        public string OutletWiseTarget
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("OutletWiseTarget"))
                    {
                        return (string)_settings["OutletWiseTarget"];
                    }
                    else
                    {
                        return "NotApplicable";
                    }
                }
                catch (Exception)
                {
                    return "NotApplicable";
                }
            }
        }

        public int NumberofDaysFastMovingCalculation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("numberofDaysFastMovingCalculation"))
                    {
                        return int.Parse(_settings["numberofDaysFastMovingCalculation"].ToString());
                    }
                    else
                    {
                        return 30;
                    }
                }
                catch (Exception)
                {
                    return 30;
                }
            }
        }

        public bool UsesFAUnify => GetBooleanSettingValue("UsesFAUnify");

        public List<string> GetPrimaryOrderType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("PrimaryOrderType"))
                    {
                        if (_settings["PrimaryOrderType"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["PrimaryOrderType"];
                        }
                        else if (_settings["PrimaryOrderType"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["PrimaryOrderType"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        // public TypeOfTaxCalculation TypeOfTaxCalculation
        // {
        //    get
        //    {
        //        try
        //        {
        //            if (settings.ContainsKey("TypeofTaxCalculation"))
        //            {
        //                if (settings["TypeofTaxCalculation"].GetType() == new List<string>().GetType())
        //                {
        //                    return (List<string>)settings["TypeofTaxCalculation"];
        //                }
        //                else if (settings["TypeofTaxCalculation"].GetType() == new Newtonsoft.Json.Linq.JArray().GetType())// needed to load Data from cache saved As Json
        //                {
        //                    return ((Newtonsoft.Json.Linq.JArray)settings["TypeofTaxCalculation"])
        //                    .Select(j => j.ToString()).ToList();
        //                }
        //                else
        //                    return new List<string> { "Default" };
        //            }
        //            else
        //                return new List<string> { "Default" }; ;
        //        }
        //        catch (Exception)
        //        {
        //            return new List<string> { "Default" }; ;
        //        }
        //    }
        // }
        public bool UsesL3MSuggestedOrderExperiment => GetBooleanSettingValue("Temp_l3m_Suggestedorderexperiment");

        public bool DistributorWiseStyleLevelMarking => GetBooleanSettingValue("DistributorWiseStyleLevelMarking");

        public bool OrderatSuperStockist => GetBooleanSettingValue("CompanyWantsTotakeOrderAtSuperStockist");

        public string CalculateAchFrom
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CalculateAchFrom"))
                    {
                        return (string)_settings["CalculateAchFrom"];
                    }
                    else
                    {
                        return "NotApplicable";
                    }
                }
                catch (Exception)
                {
                    return "NotApplicable";
                }
            }
        }

        public string ShowEmployeeSalesIn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ShowEmployeeSalesIn"))
                    {
                        return (string)_settings["ShowEmployeeSalesIn"];
                    }
                    else
                    {
                        return "Net Value";
                    }
                }
                catch (Exception)
                {
                    return "Net Value";
                }
            }
        }

        public string ShowTimerOfficialWorkUpto
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("showTimerOfficialWorkUpto"))
                    {
                        return (string)_settings["showTimerOfficialWorkUpto"];
                    }
                    else
                    {
                        return "GSM";
                    }
                }
                catch (Exception)
                {
                    return "GSM";
                }
            }
        }

        public bool UsesEnterpriseFeatures => GetBooleanSettingValue("IsUsesEnterpriseFeatures");

        public bool UsesNewOutletCreationOTPAlphanumeric => GetBooleanSettingValue("NewOutletCreationOTPAlphanumeric");

        public bool UsesDayStartSelfie => GetBooleanSettingValue("DayStartSelfie");

        public bool IsOCREnable => GetBooleanSettingValue("IsOCREnable");

        public bool RetailerOTPForOrderConfirmation => GetBooleanSettingValue("RetailerOTPForOrderConfirmation");

        // Date: 28Jan 2021
        // Logic: isOnline setting aded in ns app api company config. This setting only use when company uses online dms. This setting will be used on app side to show GTS and without GST PTR
        // one Order booking page in case main UI only
        // ASANA: https://app.asana.com/0/1201706221423127/1201708123437675/f
        public bool CompanyUsingDMSCollectionModule => GetBooleanSettingValue("CompanyUsingDMSCollectionModule");

        public bool PlotJourneyforLevel1User => GetBooleanSettingValue("PlotJourneyforLevel1User");

        public bool UsesNewAttendanceModule => GetBooleanSettingValue("UsesNewAttendanceModule");

        public bool UsesHighlightDormantSKU => GetBooleanSettingValue("UsesHighlightDormantSKU");

        public bool AllowSubDistributorCreationthroughApp =>
            GetBooleanSettingValue("AllowSubDistributorCreationthroughApp");

        public bool CompanyUsesMinimumOrderQtyForPrimaryOrders =>
            GetBooleanSettingValue("CompanyUsesMinimumOrderQtyForPrimaryOrders");

        public bool UsesAssetManagement => GetBooleanSettingValue("UsesAssetManagement");

        public bool AllowGeographicalMappingOfOutlets => GetBooleanSettingValue("AllowGeographicalMappingOfOutlets");

        public bool CompanyUsesDMSApprovalforVanLoadout =>
            GetBooleanSettingValue("CompanyUsesDMSApprovalforVanLoadout");

        public bool CompanyUsesDMSApprovalforVanLoadin => GetBooleanSettingValue("CompanyUsesDMSApprovalforVanLoadin");

        public bool GeofencingAtDistributorPoint => GetBooleanSettingValue("GeofencingAtDistributorPoint");

        public bool CompanyUsesLeaveManagement => GetBooleanSettingValue("CompanyUsesLeaveManagement");

        public bool CompanyUsesPlannedLeave => GetBooleanSettingValue("CompanyUsesPlannedLeave");

        public bool CompanyUsesCasualLeave => GetBooleanSettingValue("CompanyUsesCasualLeave");

        public bool CompanyUsesSickLeave => GetBooleanSettingValue("CompanyUsesSickLeave");
        public bool RestrictTheVisibilityOfStockValuePresentInVan => GetBooleanSettingValue("RestrictTheVisibilityOfStockValuePresentInVan");

        public bool AllowSecondaryActivityInNEWJP => GetBooleanSettingValue("AllowSecondaryActivityInNEWJP");

        public JourneyPlanVersion JourneyPlanVersion
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("JourneyPlanVersion"))
                    {
                        var journeyPlanType = ((string)_settings["JourneyPlanVersion"]).Replace(" ", string.Empty);
                        return journeyPlanType.TestNullAssign(JourneyPlanVersion.OldJourneyPlan);
                    }
                    else
                    {
                        return JourneyPlanVersion.OldJourneyPlan;
                    }
                }
                catch (Exception)
                {
                    return JourneyPlanVersion.OldJourneyPlan;
                }
            }
        }

        public bool IsUsingNewJourneyPlanStructure => JourneyPlanVersion == JourneyPlanVersion.NewJourneyPlan;

        public bool IsBeatForNewJourneyPlanStructure => JourneyPlanningEntity == JourneyPlanningEntity.Beat;

        public JourneyPlanningEntity JourneyPlanningEntity
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("JourneyPlanningEntity"))
                    {
                        var journeyPlanType = ((string)_settings["JourneyPlanningEntity"]).Replace(" ", string.Empty);
                        return journeyPlanType.TestNullAssign(JourneyPlanningEntity.Beat);
                    }
                    else
                    {
                        return JourneyPlanningEntity.Beat;
                    }
                }
                catch (Exception)
                {
                    return JourneyPlanningEntity.Beat;
                }
            }
        }

        public string SalesDataVisibilityMode
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("SalesDataVisibilityMode"))
                    {
                        return (string)_settings["SalesDataVisibilityMode"];
                    }
                    else
                    {
                        return "Value";
                    }
                }
                catch (Exception)
                {
                    return "Value";
                }
            }
        }

        public bool CompanySharesStockDetailsInWhatsAppOrder =>
            GetBooleanSettingValue("CompanySharesStockDetailsInWhatsAppOrder");

        public bool UsesNewVanLoadoutFormat => GetBooleanSettingValue("UsesNewVanLoadoutFormat");

        public bool CompanyTakesPaymentAgainstPastVanOrders =>
            GetBooleanSettingValue("CompanyTakesPaymentAgainstPastVanOrders");

        public bool FetchOutletPerformancedatafrom3rdPartyDMS =>
            GetBooleanSettingValue("FetchOutletPerformancedatafrom3rdPartyDMS");

        public bool CompanyUsesApprovalForAddingOutletToRoute =>
            GetBooleanSettingValue("CompanyUsesApprovalForAddingOutletToRoute");

        public int DaysUptoWhichUserCanTakeMonthlyDistributorStock
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DaysUptoWhichUserCanTakeMonthlyDistributorStock"))
                    {
                        return int.Parse(_settings["DaysUptoWhichUserCanTakeMonthlyDistributorStock"].ToString());
                    }
                    else
                    {
                        return 4;
                    }
                }
                catch (Exception)
                {
                    return 4;
                }
            }
        }

        public long CompanyKYCForm
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyKYCForm"))
                    {
                        return int.Parse(_settings["CompanyKYCForm"].ToString());
                    }
                    else
                    {
                        return 0;
                    }
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public bool AllowUserToSelectDistributorOnOrderConfirmation =>
            GetBooleanSettingValue("AllowUserToSelectDistributorOnOrderConfirmation");

        public bool UsersMustCompleteDistributorVisitBeforeRetailing =>
            GetBooleanSettingValue("UsersMustCompleteDistributorVisitBeforeRetailing");

        public bool Uses7DayOrderDataForVanSale => GetBooleanSettingValue("Uses7DayOrderDataForVanSale");

        public bool RestrictOrderValueMoreThanSegmentationPotential =>
            GetBooleanSettingValue("RestrictOrderValueMoreThanSegmentationPotential");

        public bool AllowSkipOTPVerification => GetBooleanSettingValue("AllowSkipOTPVerification");

        public bool CompanyShowsWarehouseStockInVanSales =>
            GetBooleanSettingValue("CompanyShowsWarehouseStockInVanSales");

        public bool Showcarouselbannerinapp => GetBooleanSettingValue("Showcarouselbannerinapp");

        public bool ShowingUsersWhoHaveNotSubmittedTourPlan =>
            GetBooleanSettingValue("ShowingUsersWhoHaveNotSubmittedTourPlan");

        public bool CompanyUsesOptionalLoadInSettlement =>
            GetBooleanSettingValue("CompanyUsesOptionalLoadInSettlement");

        public string CompanyUsesManagerApprovalForLoadOut
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesManagerApprovalForLoadOut"))
                    {
                        return (string)_settings["CompanyUsesManagerApprovalForLoadOut"];
                    }
                    else
                    {
                        return "Never";
                    }
                }
                catch (Exception)
                {
                    return "Never";
                }
            }
        }

        public bool CompanyUsesChannelWisePricing => GetBooleanSettingValue("Companyuseschannelwisepricing");

        public bool AllowUserToEditRoutePlanInApp => GetBooleanSettingValue("Allowusertoeditrouteplaninapp");

        public bool UsesAssetAllocation => GetBooleanSettingValue("UsesAssetAllocation");

        public bool UsesAssetAgreement => GetBooleanSettingValue("UsesAssetAgreement");

        public bool CompanyUsesProductRecommendation => GetBooleanSettingValue("CompanyUsesProductRecommendation");

        public bool CompanyAllowsActionAgainstOlderInvoices =>
            GetBooleanSettingValue("CompanyAllowsActionAgainstOlderInvoices");

        public string AllowPrimaryOrderBookingInNSAppTo
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("AllowprimaryorderbookinginNSAppto"))
                    {
                        return (string)_settings["AllowprimaryorderbookinginNSAppto"];
                    }
                    else
                    {
                        return "Level 1 and above";
                    }
                }
                catch (Exception)
                {
                    return "Level 1 and above";
                }
            }
        }

        public string Usercanbookprimaryorderfrom
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("Usercanbookprimaryorderfrom"))
                    {
                        return (string)_settings["Usercanbookprimaryorderfrom"];
                    }
                    else
                    {
                        return "Substockist stockist and superstockist";
                    }
                }
                catch (Exception)
                {
                    return "Substockist stockist and superstockist";
                }
            }
        }

        public bool CompanyUsesReturnsInsteadofReplacementforVanSales =>
            GetBooleanSettingValue("CompanyUsesReturnsInsteadofReplacementforVanSales");

        public bool editShopForbidden => GetBooleanSettingValue("editShopForbidden");

        public bool SecondarySchemeonGrossValue => GetBooleanSettingValue("SecondarySchemeonGrossValue");

        public bool CompanyAllowsUserToEditOutletDetailsInApp
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("Companyallowsusertoeditoutletdetailsinapp"))
                    {
                        return (bool)_settings["Companyallowsusertoeditoutletdetailsinapp"];
                    }
                    else
                    {
                        return true;
                    }
                }
                catch (Exception)
                {
                    return true;
                }
            }
        }

        public bool RestrictOrderBookingIfTentativeStockIsZero =>
            GetBooleanSettingValue("RestrictOrderBookinigIfTentativeStockIsZero");

        public bool CompanyAllowsUserDAEditing => GetBooleanSettingValue("allowDAEditing");

        public string ProductQuantityAttributesForLoadout =>
            GetStringSettingValue("ProductQuantityAttributesForLoadout");

        public string UserAboveThisLevelChangeBeatWithoutApproval =>
            GetStringSettingValue("UserAboveThisLevelChangeBeatWithoutApproval", "None");

        public string TermsAndConditionsForVanSalesInvoicePrint =>
            GetStringSettingValue("TermsAndConditionsForVanSalesInvoicePrint", "None");

        public bool CompanyAllowsUserToDivertJourneyWithoutApproval =>
            GetBooleanSettingValue("Companyallowsusertodivertjourneywithoutapproval");

        public bool UsesDifferentBillingAndShippingDetails =>
            GetBooleanSettingValue("CompanyHasDifferentBillingAndShippingDetails");

        public bool CompanyHasMoreThanThresholdProduct => GetBooleanSettingValue("CompanyHasMoreThanThresholdProduct");

        public bool IsDisablePreSalesforDSR => GetBooleanSettingValue("DisablePreSalesforDSR");

        public bool IsAllowCompanyToFilterProductsWithZeroWarehouseStock =>
            GetBooleanSettingValue("AllowCompanyToFilterProductsWithZeroWarehouseStock");

        public List<string> ReasonsForPartialSOFulfilment
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonsForPartialSOFulfilment"))
                    {
                        if (_settings["ReasonsForPartialSOFulfilment"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonsForPartialSOFulfilment"];
                        }
                        else if (_settings["ReasonsForPartialSOFulfilment"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonsForPartialSOFulfilment"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public bool CompanyUses3rdPartyAPIForCouponScheme =>
            GetBooleanSettingValue("Companyuses3rdpartyAPIforCouponscheme");

        public bool CompanyUsesCueCards => GetBooleanSettingValue("CompanyUsesCueCards");

        public bool CompanyUsesBankMasterInPaymentCollection =>
            GetBooleanSettingValue("CompanyUsesBankMasterInPaymentCollection");

        public bool CompanyUsesSequentialInvoicingUsingSalesmanCode =>
            GetBooleanSettingValue("CompanyUsesSequentialInvoicingUsingSalesmanCode");

        public string DistanceCalculationMethodology
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DistanceCalculationMethodology"))
                    {
                        return (string)_settings["DistanceCalculationMethodology"];
                    }
                    else
                    {
                        return "Event Based";
                    }
                }
                catch (Exception)
                {
                    return "Event Based";
                }
            }
        }

        public string LocationCapturingDuration
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("LocationCapturingDuration"))
                    {
                        return _settings["LocationCapturingDuration"].ToString();
                    }
                    else
                    {
                        return "20";
                    }
                }
                catch (Exception)
                {
                    return "20";
                }
            }
        }

        public bool AllowUsersToSeeTentativeStock => GetBooleanSettingValue("AllowUsersToSeeTentativeStock");

        public string DefaultDataPointerToBeShownBelowEachSKU
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DefaultDataPointerToBeShownBelowEachSKU"))
                    {
                        return (string)_settings["DefaultDataPointerToBeShownBelowEachSKU"];
                    }
                    else
                    {
                        return "None";
                    }
                }
                catch (Exception)
                {
                    return "None";
                }
            }
        }

        public bool CompanyUsesEInvoicing => GetBooleanSettingValue("CompanyUsesEInvoicing");

        public bool CompanyUsesEnhancedWhatsAppPDF => GetBooleanSettingValue("CompanyUsesEnhancedWhatsAppPDF");

        public string usercanbooksecondaryorderfrom
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("usercanbooksecondaryorderfrom"))
                    {
                        return (string)_settings["usercanbooksecondaryorderfrom"];
                    }
                    else
                    {
                        return "SubstockistStockistSuperstockist";
                    }
                }
                catch (Exception)
                {
                    return "SubstockistStockistSuperstockist";
                }
            }
        }

        public bool CompanyUsesAttendanceBasedTADA => GetBooleanSettingValue("CompanyUsesAttendanceBasedTADA");

        public bool CompanyUsesAdditionalStockForVanSales =>
            GetBooleanSettingValue("CompanyUsesAdditionalStockForVanSales");

        public string MultipleSchemesWithSameSKUs
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MultipleSchemesWithSameSKUs"))
                    {
                        return _settings["MultipleSchemesWithSameSKUs"].ToString();
                    }
                    else
                    {
                        return "Apply lowest Geo Hierarchy scheme";
                    }
                }
                catch (Exception)
                {
                    return "Apply lowest Geo Hierarchy scheme";
                }
            }
        }

        public bool IsCompanyUsingProductRecommendationEngine =>
            GetBooleanSettingValue("IsCompanyUsingProductRecommendationEngine");

        public bool CompanyUsesAssetReallocation => GetBooleanSettingValue("CompanyUsesAssetReallocation");

        public bool AllowDuplicationCheckBasedOnPDThroughCustomTags =>
            GetBooleanSettingValue("AllowDuplicationCheckBasedOnPDThroughCustomTags");

        public bool UsesJourneyCalendar => GetBooleanSettingValue("UsesJourneyCalendar");

        public virtual NumberSystems NumberSystem
        {
            get
            {
                var numberSystem = NumberSystems.Indian;
                try
                {
                    numberSystem = (NumberSystems?)GetCountryInfo()?.numberSystem ?? numberSystem;
                }
                catch (Exception)
                {
                    return numberSystem;
                }

                return numberSystem;
            }
        }

        public CompanySeesDataInEnum CompanySeesDataIn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("SalesDataVisibilityMode"))
                    {
                        var companySeesDataIn =
                            ((string)_settings["SalesDataVisibilityMode"]).Replace(" ", string.Empty);
                        return companySeesDataIn.TestNullAssign(CompanySeesDataInEnum.SuperUnit);
                    }

                    return CompanySeesDataInEnum.SuperUnit;
                }
                catch (Exception)
                {
                    return CompanySeesDataInEnum.SuperUnit;
                }
            }
        }

        public bool UsesSalesinAmount => GetBooleanSettingValue("UsesSalesinQuantity");

        public bool AddOutletRequestWithOrderBooking => GetBooleanSettingValue("AddOutletRequestWithOrderBooking");

        public bool ManagerDoesPrimaryOperationsInDistributorVisit =>
            GetBooleanSettingValue("ManagerDoesPrimaryOperationsInDistributorVisit");

        public bool AutoGenerateAssetReferenceNumber => GetBooleanSettingValue("AutoGenerateAssetReferenceNumber");

        public bool ShowOutletStatsOnPrepScreen => GetBooleanSettingValue("showOutletStatsOnPrepScreen");

        public bool ShowOutletPreparationScreenDataInStandardUnit => GetBooleanSettingValue("ShowOutletPreparationScreenDataInStandardUnit");

        public bool CollectFeedbackOnlyForUnsoldSKUs => GetBooleanSettingValue("CollectFeedbackOnlyForUnsoldSKUs");

        public string FeedbackCollectionForProductRecommendation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("FeedbackCollectionForProductRecommendation"))
                    {
                        return (string)_settings["FeedbackCollectionForProductRecommendation"];
                    }
                    else
                    {
                        return "Not visible";
                    }
                }
                catch (Exception)
                {
                    return "Not visible";
                }
            }
        }

        public List<string> FeedbackOptionsForProductReccomendation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("FeedbackOptionsForProductReccomendation"))
                    {
                        if (_settings["FeedbackOptionsForProductReccomendation"].GetType() ==
                            new List<string>().GetType())
                        {
                            return (List<string>)_settings["FeedbackOptionsForProductReccomendation"];
                        }
                        else if (_settings["FeedbackOptionsForProductReccomendation"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["FeedbackOptionsForProductReccomendation"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public TargetValueType OutletTargetOn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("Outlettargeton"))
                    {
                        var outletTargetOn = (string)_settings["Outlettargeton"];
                        return outletTargetOn.TestNullAssign(Libraries.CommonEnums.TargetValueType.Revenue);
                    }

                    return Libraries.CommonEnums.TargetValueType.Revenue;
                }
                catch (Exception)
                {
                    return Libraries.CommonEnums.TargetValueType.Revenue;
                }
            }
        }

        public bool UsesthirdPartyAPIforInvoiceAchievement =>
            GetBooleanSettingValue("UsethirdPartyAPIforInvoiceAchievement");

        public bool CompanyUses3rdBookingUnit => GetBooleanSettingValue("CompanyUses3rdBookingUnit");

        public List<string> CategoryProductivityKPICategories
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CategoryProductivityKPICategories"))
                    {
                        if (_settings["CategoryProductivityKPICategories"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["CategoryProductivityKPICategories"];
                        }
                        else if (_settings["CategoryProductivityKPICategories"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["CategoryProductivityKPICategories"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public bool PurchasePriceInclusiveOfTaxInIndia => GetBooleanSettingValue("PurchasePriceInclusiveOfTaxInIndia");

        public string CountOfNearBYBuyingStoreProductRecommendation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CountOfNearBYBuyingStore-ProductRecommendation"))
                    {
                        return (string)_settings["CountOfNearBYBuyingStore-ProductRecommendation"];
                    }
                    else
                    {
                        return "Percentage";
                    }
                }
                catch (Exception)
                {
                    return "Percentage";
                }
            }
        }

        public bool IsCompanyUsingProductWinback => GetBooleanSettingValue("IsCompanyUsingProductWinback");

        public bool CompanyAllowsDSRToEditLoadIn => GetBooleanSettingValue("CompanyAllowsDSRToEditLoadIn");

        public bool CompanyAllowsLoadoutAfterStockNormViolation =>
            GetBooleanSettingValue("CompanyAllowsLoadoutAfterStockNormViolation");

        public bool JourneyDiversionApprovalOnlyToAdmin =>
            GetBooleanSettingValue("JourneyDiversionApprovalOnlyToAdmin");

        public bool CompanyUsesGSTInPrimaryOrder => GetBooleanSettingValue("CompanyUsesGSTInPrimaryOrder");

        public bool usesLast10Invoices => GetBooleanSettingValue("usesLastThirtyDayInvoice");

        public bool DistributorWisePricingInTelephonicOrder =>
            GetBooleanSettingValue("DistributorWisePricingInTelephonicOrder");

        public bool CompanyUsesFA_IR => GetBooleanSettingValue("CompanyUsesFA_IR");

        public List<string> ImageCategoriesForIR
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ImageCategoriesForIR"))
                    {
                        if (_settings["ImageCategoriesForIR"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ImageCategoriesForIR"];
                        }
                        else if (_settings["ImageCategoriesForIR"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ImageCategoriesForIR"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public bool CompanyUsesSecondaryPJP => GetBooleanSettingValue("CompanyUsesSecondaryPJP");

        public string BeatTargetValueType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("Beatwisetargetvaluetype"))
                    {
                        return (string)_settings["Beatwisetargetvaluetype"];
                    }
                    else
                    {
                        return "Revenue";
                    }
                }
                catch (Exception)
                {
                    return "Revenue";
                }
            }
        }

        public bool AbleToTakeStockOnMustSell => GetBooleanSettingValue("AbleToTakeStockOnMustSell");

        public bool ApparelCompanyUsesBasketsAndSets => GetBooleanSettingValue("ApparelCompanyUsesBasketsAndSets");

        public bool AllowUserToInputPOandDateOfDelivery =>
            GetBooleanSettingValue("AllowUserToInputPOandDateOfDelivery");

        public bool CompanyDefinesPricingOnSuperStockistLevel =>
            GetBooleanSettingValue("CompanyDefinesPricingOnSuperStockistLevel");

        public long OpportunityTagId
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("OpportunityTagId"))
                    {
                        return (long)_settings["OpportunityTagId"];
                    }

                    return 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public string AuthenticationForGSTAPI => GetStringSettingValue("AuthenticationForGSTAPI");

        public string APIForGSTVerification => GetStringSettingValue("APIForGSTVerification");

        public string CompanyUsesHandHeldPrinterType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesHandHeldPrinterType"))
                    {
                        return (string)_settings["CompanyUsesHandHeldPrinterType"];
                    }

                    return "Medium";
                }
                catch (Exception)
                {
                    return "Medium";
                }
            }
        }

        public bool CompanyUsesMapView => GetBooleanSettingValue("CompanyUsesMapView");

        public string UserCanTakeDistributorStockFrom
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UserCanTakeDistributorStockFrom"))
                    {
                        return (string)_settings["UserCanTakeDistributorStockFrom"];
                    }

                    return "All";
                }
                catch (Exception)
                {
                    return "All";
                }
            }
        }

        public string OutletMarginIsCalculatedBasedOn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("OutletMarginIsCalculatedBasedOn"))
                    {
                        return (string)_settings["OutletMarginIsCalculatedBasedOn"];
                    }

                    return "PTR";
                }
                catch (Exception)
                {
                    return "PTR";
                }
            }
        }

        public bool SubOrdinateBeatsToBeShownTillHighestLevel =>
            GetBooleanSettingValue("SubOrdinateBeatsToBeShownTillHighestLevel");

        public bool CompanyUsesHCCBUserFlows => GetBooleanSettingValue("CompanyUsesHCCBUserFlows");

        public bool CompanyUsesKPIBasedBeatometer => GetBooleanSettingValue("CompanyUsesKPIBasedBeatometer");

        public bool CompanyUsesPositionPDMapping => GetBooleanSettingValue("CompanyUsesPositionPDMapping");

        public bool CompanyAllowsUserToTakeEveningLoadOut =>
            GetBooleanSettingValue("CompanyAllowsUserToTakeEveningLoadOut");

        public bool CompanyUsesTaskManagement => GetBooleanSettingValue("CompanyUsesTaskManagement");

        public bool CompanyUsesUserKPIDashboard => GetBooleanSettingValue("CompanyUsesUserKPIDashboard");

        public bool CompanyUsesSubDGeographicalMapping => GetBooleanSettingValue("CompanyUsesSubDGeographicalMapping");

        public bool CompanySettlesWithKeepInVan => GetBooleanSettingValue("CompanySettlesWithKeepInVan");

        public bool CompanyHasNonRouteChangeOVT => GetBooleanSettingValue("CompanyHasNonRouteChangeOVT");

        public bool CompanyDoesNotAllowDSRToRejectAdditionalLoadout =>
            GetBooleanSettingValue("CompanyDoesNotAllowDSRToRejectAdditionalLoadout");

        public bool CompanyUsesProductTagSuggestions =>
            GetBooleanSettingValue("CompanyUsesProductTagSuggestions");

        public bool usesQPSScheme =>
            GetBooleanSettingValue("usesQPSScheme");

        public bool CompanyUsesHotsellingOutlets =>
            GetBooleanSettingValue("CompanyUsesHotsellingOutlets");

        public bool CompanyRequiresAutoApprovedData => GetBooleanSettingValue("CompanyRequiresAutoApprovedData");

        public bool CompanyUseExternalAPIForDSRDayEnd => GetBooleanSettingValue("CompanyUseExternalAPIForDSRDayEnd");

        public bool ShowDynamicFilterOnOutletListScreen =>
            GetBooleanSettingValue("ShowDynamicFilterOnOutletListScreen");

        public bool CompanyAllowReordersInCaseOfReviewOrder
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyAllowReordersInCaseOfReviewOrder"))
                    {
                        return (bool)_settings["CompanyAllowReordersInCaseOfReviewOrder"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsersCanSeeCasePriceInsteadOfMRP
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsersCanSeeCasePriceInsteadOfMRP"))
                    {
                        return (bool)_settings["CompanyUsersCanSeeCasePriceInsteadOfMRP"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesFlexibleTargetModule
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesFlexibleTargetModule"))
                    {
                        return (bool)_settings["CompanyUsesFlexibleTargetModule"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyWantsToShowPowerBIInGTApp
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyWantsToShowPowerBIInGTApp"))
                    {
                        return (bool)_settings["CompanyWantsToShowPowerBIInGTApp"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesSchemeApproval
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesSchemeApproval"))
                    {
                        return (bool)_settings["CompanyUsesSchemeApproval"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesSRWiseBudgetAllocation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesSRWiseBudgetAllocation"))
                    {
                        return (bool)_settings["CompanyUsesSRWiseBudgetAllocation"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyWantsToShowOrderStatusInApp
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyWantsToShowOrderStatusInApp"))
                    {
                        return (bool)_settings["CompanyWantsToShowOrderStatusInApp"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyBlocksOrderOnPreDefinedDates
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyBlocksOrderOnPreDefinedDates"))
                    {
                        return (bool)_settings["CompanyBlocksOrderOnPreDefinedDates"];
                    }

                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public List<string> AssetStatusOptions
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("AssetStatusOptions"))
                    {
                        if (_settings["AssetStatusOptions"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["AssetStatusOptions"];
                        }
                        else if (_settings["AssetStatusOptions"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["AssetStatusOptions"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return new List<string>();
                        }
                    }
                    else
                    {
                        return new List<string>();
                    }
                }
                catch (Exception)
                {
                    return new List<string>();
                }
            }
        }

        public PositionCodeLevel? AllowsL1UserWithVacantLevelToChangeBeat
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("AllowsL1UserWithVacantLevelToChangeBeat"))
                    {
                        if (_settings["AllowsL1UserWithVacantLevelToChangeBeat"] == null)
                        {
                            return null;
                        }

                        return (PositionCodeLevel)Enum.Parse(typeof(PositionCodeLevel),
                            (string)_settings["AllowsL1UserWithVacantLevelToChangeBeat"]);
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error parsing 'AllowsL1UserWithVacantLevelToChangeBeat': {ex.Message}");
                    return null;
                }
            }
        }

        public bool EnableOrderRestriction => GetBooleanSettingValue("EnableOrderRestriction");

        public bool CompanyAllowMultipleDeviceLogin
            => GetBooleanSettingValue("isAllowMultipleDeviceLogin");

        public bool CompanyAllowsLoadoutIntentAsLoadout =>
            GetBooleanSettingValue("CompanyAllowsLoadoutIntentAsLoadout");

        public double ConversionFactorForSecondaryCurrencyV2 =>
           GetDoubleSettingValue("ConversionFactorForSecondaryCurrencyV2");

        // try
        // {
        //     if (settings.ContainsKey("CompanyAllowsLoadoutIntentAsLoadout"))
        //     {
        //         return (bool)settings["CompanyAllowsLoadoutIntentAsLoadout"];
        //     }
        //
        //     return false;
        // }
        // catch (Exception)
        // {
        //     return false;
        // }
        private bool GetBooleanSettingValue(string key, bool defaultValue = false)
        {
            try
            {
                if (_settings.TryGetValue(key, out var setting))
                {
                    return (bool)setting;
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(
                    $"Error in fetching company setting: {key} Error: " + ex.Message);
                return defaultValue;
            }
        }

        private double GetDoubleSettingValue(string key, double defaultValue = 0.0)
        {
            try
            {
                if (_settings.TryGetValue(key, out var setting))
                {
                    return (double)setting;
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(
                    $"Error in fetching company setting: {key} Error: " + ex.Message);
                return defaultValue;
            }
        }

        private string GetStringSettingValue(string key, string defaultValue = "No Data")
        {
            try
            {
                if (_settings.TryGetValue(key, out var setting))
                {
                    return (string)setting;
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(
                    $"Error in fetching company setting: {key} Error: " + ex.Message);

                return defaultValue;
            }
        }

        public bool IsCompanyUsesNormBasedAttendance
            => GetBooleanSettingValue("UsesNewAttendanceModule");

        public bool MakeAssetAuditingMandatory
            => GetBooleanSettingValue("MakeAssetAuditingMandatory");

        public bool AllowOrderCancellation
            => GetBooleanSettingValue("AllowOrderCancellation");

        public bool CompanyAllowsL1UserToCreateSubDistributorIntheApp
            => GetBooleanSettingValue("CompanyAllowsL1UserToCreateSubDistributorIntheApp");

        public bool CompanyRestrictsPrimaryOrderBookingInUnit
            => GetBooleanSettingValue("CompanyRestrictsPrimaryOrderBookingInUnit");

        public bool CompanyAllowEditPTR
            => GetBooleanSettingValue("CompanyAllowEditPTR");

        public bool CompanyUsesDisplayCategoryasSuperCode
            => GetBooleanSettingValue("Company Uses Display Category as SuperCode");

        public bool RestrictScreenRecording
            => GetBooleanSettingValue("RestrictScreenRecording");

        public bool UsesPrimaryPositionCodeOnly
            => GetBooleanSettingValue("UsesPrimaryPositionCodeOnly");

        public bool CompanyUsesGeofencingAtAddToRouteFeature
            => GetBooleanSettingValue("CompanyUsesGeofencingAtAddToRouteFeature");

        public bool CompanyUsesFATradeApp
            => GetBooleanSettingValue("CompanyUsesFATradeApp");

        public bool whatsappOrderToDistributor
            => GetBooleanSettingValue("whatsappOrderToDistributor", true);

        public bool CompanyUsesMRPBatchBasedOrderBooking
            => GetBooleanSettingValue("CompanyUsesMRPBatchBasedOrderBooking");

        public bool CompanyUsesOnlinePaymentMode
            => GetBooleanSettingValue("CompanyUsesOnlinePaymentMode");

        public bool CompanyAllowsSubDCreationByISR
            => GetBooleanSettingValue("CompanyAllowsSubDCreationByISR");

        public bool CompanyUsesPerfectStore
            => GetBooleanSettingValue("CompanyUsesPerfectStore");

        public bool CompanyFetchPrimaryVsSecondaryDataFrom3rdPartyDMS
            => GetBooleanSettingValue("CompanyFetchPrimaryVsSecondaryDataFrom3rdPartyDMS");

        public bool CompanyUsesEInvoicingInKenya
            => GetBooleanSettingValue("CompanyUsesEInvoicingInKenya");

        public bool NonPJPVisitLock
            => GetBooleanSettingValue("NonPJPVisitLock");

        public bool CompanyMigratesToFlutter
            => GetBooleanSettingValue("CompanyMigratesToFlutter");

        public bool CompanyUsesPreferredSchemes
            => GetBooleanSettingValue("CompanyUsesPreferredSchemes");

        public bool CompanyUsesPerfectStoreRewards
            => GetBooleanSettingValue("CompanyUsesPerfectStoreRewards");

        public bool CompanyUsesFactorySelect
            => GetBooleanSettingValue("Companyusesfactoryselect");

        public bool EnableRuralMGRFlowGTforHCCB
            => GetBooleanSettingValue("EnableRuralMGRFlowGTforHCCB");

        public bool CompanyUsesAdvanceSearchFeature
            => GetBooleanSettingValue("CompanyUsesAdvanceSearchFeature");

        public string CompanyUsesMetricCalculationType
            => GetStringSettingValue("CompanyUsesMetricCalculationType");

        public bool CompanyUsesDynamicBanner
            => GetBooleanSettingValue("CompanyUsesDynamicBanner");

        public bool EnableLCTargetNudgeOnOrderBooking
            => GetBooleanSettingValue("EnableLCTargetNudgeOnOrderBooking");

        public bool CompanyUsesWarehouseManagement
            => GetBooleanSettingValue("CompanyUsesWarehouseManagement");

        public bool AllowSingleSelectFilterOnOrderBooking
            => GetBooleanSettingValue("AllowSingleSelectFilterOnOrderBooking");

        public bool HideAddressOnOutletListScreen
            => GetBooleanSettingValue("HideAddressOnOutletListScreen");

        public bool CompanyUsesOnlyGST
            => GetBooleanSettingValue("CompanyUsesOnlyGST");

        public bool CompanyCreatesPlanOnPositionCodes
            => GetBooleanSettingValue("CompanyCreatesPlanOnPositionCodes");

        public bool CompanyUsesSameDayOrderCancellation
            => GetBooleanSettingValue("CompanyUsesSameDayOrderCancellation");

        public bool UserCanRaiseOrderCancellationRequest
            => GetBooleanSettingValue("UserCanRaiseOrderCancellationRequest");

        public string CompanyUsesCallReviewDuration
            => GetStringSettingValue("CompanyUsesCallReviewDuration");

        public bool CompanyUsesPerformanceManagementSystem
            => GetBooleanSettingValue("CompanyUsesPerformanceManagementSystem");

        public bool CompanyAllowMultipleBeatDayStart
            => GetBooleanSettingValue("CompanyAllowMultipleBeatDayStart");

        public bool canSearchOutletsAcrossBeats
            => GetBooleanSettingValue("canSearchOutletsAcrossBeats");

        public bool UploadsPurchaseOrder
            => GetBooleanSettingValue("UploadsPurchaseOrder");

        public bool AddOutletToRouteviaDuplicationCheck
            => GetBooleanSettingValue("AddOutletToRouteviaDuplicationCheck");

        public bool MandatoryRetailerStockcapture
            => GetBooleanSettingValue("MandatoryRetailerStockcapture");

        public List<string> ReasonForOrderCancellation
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForOrderCancellation"))
                    {
                        if (_settings["ReasonForOrderCancellation"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonForOrderCancellation"];
                        }
                        else if (_settings["ReasonForOrderCancellation"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray().GetType())
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonForOrderCancellation"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return new List<string>();
                        }
                    }
                    else
                    {
                        return new List<string>();
                    }
                }
                catch (Exception)
                {
                    return new List<string>();
                }
            }
        }

        public bool NewApprovalMechanism
            => GetBooleanSettingValue("NewApprovalMechanism");

        public List<string> CompanyUsesProductDisclaimerText
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesProductDisclaimerText"))
                    {
                        if (_settings["CompanyUsesProductDisclaimerText"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["CompanyUsesProductDisclaimerText"];
                        }
                        else if (_settings["CompanyUsesProductDisclaimerText"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray().GetType())
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["CompanyUsesProductDisclaimerText"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return new List<string>();
                        }
                    }
                    else
                    {
                        return new List<string>();
                    }
                }
                catch (Exception)
                {
                    return new List<string>();
                }
            }
        }

        public bool ShowDiscountAmountInFocScheme
            => GetBooleanSettingValue("ShowDiscountAmountInFocScheme");

        public bool SaveManagerIdInsteadOfPositionUserId
            => GetBooleanSettingValue("SaveManagerIdInsteadOfPositionUserId");

        public bool CompanyUsesFACare
            => GetBooleanSettingValue("CompanyUsesFACare");

        private int GetIntegerSettingValue(string key, int defaultValue = 0)
        {
            try
            {
                if (_settings.TryGetValue(key, out var setting))
                {
                    return Convert.ToInt32(setting);
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error fetching company setting: {key} Error: {ex.Message}");
                return defaultValue;
            }
        }

        public int GetThresholdPercentageForSchemeReco
            => GetIntegerSettingValue("ThresholdPercentageForSchemeReco", 75);

        public bool IsHccbIRLogicKey
            => GetBooleanSettingValue("HccbIRLogicKey");

        public bool CompanyUsesSimplifiedSFAFlow
            => GetBooleanSettingValue("CompanyUsesSimplifiedSFAFlow");

        public bool IsAllowMultipleDeviceLogin => GetBooleanSettingValue("isAllowMultipleDeviceLogin", true);

        public bool CompanyUsesDistributorStock => GetBooleanSettingValue("CompanyUsesDistributorStock", false);

        public int CompanyShowsNumberOfDecimalPlacesInApp
            => GetIntegerSettingValue("CompanyShowsNumberOfDecimalPlacesInApp", 0);

        public int CompanyAllowsCurrencyOffsetUpto
           => GetIntegerSettingValue("CompanyAllowsCurrencyOffsetUpto", 0);

        public bool CompanyUsesExtendedOutletInformation
            => GetBooleanSettingValue("CompanyUsesExtendedOutletInfor");

        public int ConversionFactorForSecondaryCurrency
            => GetIntegerSettingValue("ConversionFactorForSecondaryCurrency", 0);

        public bool CaptureLocationImagewhileEdit
            => GetBooleanSettingValue("CaptureLocationImagewhileEdit", false);

        public bool ImplementLoggerForVanSales
           => GetBooleanSettingValue("ImplementLoggerForVanSales", false);

        public bool RestrictOrderBookinginLoosePairForPrimaryOrders
            => GetBooleanSettingValue("RestrictOrderBookinginLoosePairForPrimaryOrders", false);

        public bool DisplayAdditionalUnit
            => GetBooleanSettingValue("DisplayAdditionalUnit", false);

        public int yearStartMonth
        => GetIntegerSettingValue("yearStartMonth", 4);
        public int DigitsInPhNo
        => GetIntegerSettingValue("DigitsInPhNo", 10);

        public bool IsUsesNewJourneyPlan
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("JourneyPlanVersion") &&
                        (string)_settings["JourneyPlanVersion"] == "New Journey Plan")
                    {
                        return true;
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool E11Company
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("E11Company"))
                    {
                        return (bool)_settings["E11Company"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool TertiaryOfftakeManual
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("tertiaryOfftakeManual"))
                    {
                        return (bool)_settings["tertiaryOfftakeManual"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool UsesEngageWhatsappIntegration
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesEngageWhatsappIntegration"))
                    {
                        return (bool)_settings["CompanyUsesEngageWhatsappIntegration"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesMonthWiseOpening
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UsesMonthWiseOpeningStockMT"))
                    {
                        return (bool)_settings["UsesMonthWiseOpeningStockMT"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public PortalUserRole GetCompanyHighestHierarchy
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesHighestHierarchy"))
                    {
                        var highestHierarchy = (string)_settings["CompanyUsesHighestHierarchy"];
                        return highestHierarchy.TestNullAssign(PortalUserRole.GlobalSalesManager);
                    }

                    return PortalUserRole.GlobalSalesManager;
                }
                catch (Exception)
                {
                    return PortalUserRole.GlobalSalesManager;
                }
            }
        }

        public PositionCodeLevel GetCompanyHighestPositionLevel
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("HighestPositionLevel"))
                    {
                        var highestHierarchy = ((string)_settings["HighestPositionLevel"])
                            .PositionCodeLevelFromPositionLevel();
                        return highestHierarchy.TestNullAssign(PositionCodeLevel.L8Position);
                    }

                    return PositionCodeLevel.L4Position;
                }
                catch (Exception)
                {
                    return PositionCodeLevel.L4Position;
                }
            }
        }

        public string TargetValueType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("TargetValueType"))
                    {
                        return (string)_settings["TargetValueType"];
                    }

                    return "Revenue";
                }
                catch (Exception)
                {
                    return "Revenue";
                }
            }
        }

        public string CompanyHasPrimaryVanReconciliationOperationAs
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyHasPrimaryVanReconciliationOperationAs"))
                    {
                        return (string)_settings["CompanyHasPrimaryVanReconciliationOperationAs"];
                    }

                    return "KeepInVanWithUnloadAll";
                }
                catch (Exception)
                {
                    return "KeepInVanWithUnloadAll";
                }
            }
        }

        public string OpenMarketStockRequestApprovalIsDoneVia
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("OpenMarketStockRequestApprovalIsDoneVia"))
                    {
                        return (string)_settings["OpenMarketStockRequestApprovalIsDoneVia"];
                    }

                    return "SMS";
                }
                catch (Exception)
                {
                    return "SMS";
                }
            }
        }

        public string CompanyActionOnVanCapacityBreach
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyActionOnVanCapacityBreach"))
                    {
                        return (string)_settings["CompanyActionOnVanCapacityBreach"];
                    }

                    return "";
                }
                catch (Exception)
                {
                    return "";
                }
            }
        }

        public bool UsingIntelligentScheme
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("usesIntelligentSchemes"))
                    {
                        return (bool)_settings["usesIntelligentSchemes"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool UsesProductDivision
            => GetBooleanSettingValue("usesDistProdDivBeatMappings");

        public bool CompanyUsesProofOfDeliveryForOrdersAndinvoices
            => GetBooleanSettingValue("CompanyUsesProofOfDeliveryForOrdersAndinvoices");

        public bool usesReverseGeocodes
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("usesReverseGeocodes"))
                    {
                        return (bool)_settings["usesReverseGeocodes"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        //public string OrderBookingScreenType
        //{
        //    get
        //    {
        //        try
        //        {
        //            if (_settings.ContainsKey("AppOrderBookingScreenType"))
        //            {
        //                return (string)_settings["AppOrderBookingScreenType"];
        //            }

        //            return "Main";
        //        }
        //        catch (Exception)
        //        {
        //            return "Main";
        //        }
        //    }
        //}

        public int AssortedProductFlagDays
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("assortedProductFlagDays"))
                    {
                        return int.Parse(_settings["assortedProductFlagDays"].ToString());
                    }

                    return 30;
                }
                catch (Exception)
                {
                    return 30;
                }
            }
        }

        public bool UsesScheduledCallProductivity
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ScheduledCallProductivity"))
                    {
                        return bool.Parse(_settings["ScheduledCallProductivity"].ToString());
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public int TargetType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("TargetsType"))
                    {
                        return (int)(long)_settings["TargetsType"];
                    }

                    return 1;
                }
                catch (Exception)
                {
                    return 1;
                }
            }
        }

        public bool UsesAddOutletRequest
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesAddOutletRequest"))
                    {
                        return (bool)_settings["CompanyUsesAddOutletRequest"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public string PrimaryTargetsOn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("PrimaryTargetsOn"))
                    {
                        return (string)_settings["PrimaryTargetsOn"];
                    }

                    return "No Targets";
                }
                catch (Exception)
                {
                    return "No Targets";
                }
            }
        }

        public string PrimarySalesSource
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("PrimarySalesSource"))
                    {
                        return (string)_settings["PrimarySalesSource"];
                    }

                    return "Bulk Upload";
                }
                catch (Exception)
                {
                    return "Bulk Upload";
                }
            }
        }

        public List<string> GetRejectionReasons
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForOutletRequestRejection"))
                    {
                        return (List<string>)_settings["ReasonForOutletRequestRejection"];
                    }

                    return null;
                }
                catch
                {
                    return null;
                }
            }
        }

        public bool isManagerDoingPrimaryWork
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ManagerDoesPrimaryOperationsInDistributorVisit"))
                    {
                        return (bool)_settings["ManagerDoesPrimaryOperationsInDistributorVisit"];
                    }

                    return false;
                }
                catch
                {
                    return false;
                }
            }
        }

        //Date: 19-Aug.2020
        //ASANA: https://app.asana.com/0/43051116721058/1189527697385554/f
        //Logic: We have assmumption Report data will come as it store in Reprt DB. This setting need for temporary as I am adding check on Report as checking
        public bool ShouldCalculateBeatLength
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("allowcompaniestocalculateBeatLength"))
                    {
                        return (bool)_settings["allowcompaniestocalculateBeatLength"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public double CompanyPricePerUser
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("PricePerUser"))
                    {
                        return Convert.ToDouble(_settings["PricePerUser"]);
                    }

                    return 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public int MinimumBillingUser
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MinimumBillingUser"))
                    {
                        return Convert.ToInt32(_settings["MinimumBillingUser"]);
                    }

                    return 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public bool UserUsesNSApp
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UserUsesNSApp"))
                    {
                        return (bool)_settings["UserUsesNSApp"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool UsesSingleApprovalForOutletAdditionRequest
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UsesSingleApprovalForOutletAdditionRequest"))
                    {
                        return (bool)_settings["UsesSingleApprovalForOutletAdditionRequest"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public string BillingType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("BillingType"))
                    {
                        return _settings["BillingType"].ToString();
                    }

                    return "Monthly";
                }
                catch (Exception)
                {
                    return "Monthly";
                }
            }
        }

        public string OutletTargetType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("OutletTargetType"))
                    {
                        return (string)_settings["OutletTargetType"];
                    }

                    return "Overall";
                }
                catch (Exception)
                {
                    return "Overall";
                }
            }
        }

        public bool SeeReportOnBasisOfPositionCode
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("SeeReportOnBasisOfPositionCode"))
                    {
                        return (bool)_settings["SeeReportOnBasisOfPositionCode"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public GeographyLevel CompanyHighestGeography
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("HighestGeoHierarchy"))
                    {
                        var journeyPlanType = (string)_settings["HighestGeoHierarchy"];
                        return journeyPlanType.TestNullAssign(GeographyLevel.Level4);
                    }

                    return GeographyLevel.Level4;
                }
                catch (Exception)
                {
                    return GeographyLevel.Level4;
                }
            }
        }

        public long? WFHSurveyId
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("WFHSurveyId"))
                    {
                        return (long?)_settings["WFHSurveyId"];
                    }

                    return 0;
                }
                catch (Exception)
                {
                    return 0;
                }
            }
        }

        public bool UsesZonalJourneyCycle
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UsesZonalJourneyCycle"))
                    {
                        return (bool)_settings["UsesZonalJourneyCycle"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool IsRouteForNewJourneyPlanStructure =>
            IsUsingNewJourneyPlanStructure && JourneyPlanningEntity == JourneyPlanningEntity.Route;

        public bool IsCompanyUsesAttendanceBasedTADA
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesAttendanceBasedTADA"))
                    {
                        return (bool)_settings["CompanyUsesAttendanceBasedTADA"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool IsCallReviewAllowed
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("callReviewAllowed"))
                    {
                        return (bool)_settings["callReviewAllowed"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool DownloadReportInExcel
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DownloadReportInExcel"))
                    {
                        return (bool)_settings["DownloadReportInExcel"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool IsUnifyEnabled
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UsesFAUnify"))
                    {
                        return (bool)_settings["UsesFAUnify"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesManualRecommendations
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesManualRecommendations"))
                    {
                        return (bool)_settings["CompanyUsesManualRecommendations"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public bool CompanyUsesOpenMarket
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesOpenMarketOperations"))
                    {
                        return (bool)_settings["CompanyUsesOpenMarketOperations"];
                    }

                    return false;
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        public string UsesNetValueForEmployeeSales
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ShowEmployeeSalesIn"))
                    {
                        return (string)_settings["ShowEmployeeSalesIn"];
                    }

                    return null;
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public DayOfWeek DayOfCompanyWideWeeklyOffForUsers
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DayOfCompanyWideWeeklyOffForUsers"))
                    {
                        var dayoff = (string)_settings["DayOfCompanyWideWeeklyOffForUsers"];
                        return dayoff.TestNullAssign(DayOfWeek.Sunday);
                    }

                    return DayOfWeek.Sunday;
                }
                catch (Exception)
                {
                    return DayOfWeek.Sunday;
                }
            }
        }

        public CallAdherenceBasis BasisForCallAdherenceCalc =>
            GetSettingEnum("seeCallAdherenceOnBasisOf", CallAdherenceBasis.TC);

        private bool GetBooleanSettingValue(string key)
        {
            try
            {
                if (_settings.TryGetValue(key, out var setting))
                {
                    return (bool)setting;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(
                    $"Error in fetching company setting: {key} Error: " + ex.Message);

                return false;
            }
        }

        private string GetSettingByKey(string key)
        {
            if (_settings.TryGetValue(key, out var setting))
            {
                return setting.ToString();
            }

            return null;
        }

        private T GetSettingEnum<T>(string key, T defaultValue) where T : struct
        {
            try
            {
                var result = defaultValue;
                var value = GetSettingByKey(key).Replace(" ", "");
                if (!string.IsNullOrEmpty(value))
                {
                    Enum.TryParse(value, out result);
                }

                return result;
            }
            catch (Exception)
            {
                return defaultValue;
            }
        }

        public string OrderRestrictBasedOnOutletPotential =>
            GetStringSettingValue("ShowWarning/OrderRestrictBasedOnOutletPotential", "None");

        public string CompanyRequiresFollowingDataOnAllScreens =>
            GetStringSettingValue("CompanyRequiresFollowingDataOnAllScreens", "Distributor");

        public bool ShowMRPAgainstEachProductInApparelUI =>
            GetBooleanSettingValue("ShowMRPAgainstEachProductInApparelUI");

        public bool CompanyUsesEmailVerification =>
            GetBooleanSettingValue("CompanyUsesEmailVerification");

        public bool GulfGRSRequest =>
            GetBooleanSettingValue("GulfGRSRequest");

        public bool EnablePANVerification =>
            GetBooleanSettingValue("EnablePANVerification");
        public bool ManualDAApproval => GetBooleanSettingValue("ManualDAApproval");

        public bool DisableLocationFieldAfterAutoCapture
            => GetBooleanSettingValue("DisableLocationFieldAfterAutoCapture");

        public bool CompanyUsesOnlyNewFlutterApp
            => GetBooleanSettingValue("CompanyUsesOnlyNewFlutterApp");

        public bool ApplySchemesSuccessively
            => GetBooleanSettingValue("ApplySchemesSuccessively");

        public string SupportEmail
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("SupportEmail"))
                    {
                        return (string)_settings["SupportEmail"];
                    }

                    return "<EMAIL>";
                }
                catch (Exception)
                {
                    return "<EMAIL>";
                }
            }
        }

        public string SupportPhone
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("SupportPhone"))
                    {
                        return (string)_settings["SupportPhone"];
                    }

                    return "011-411-70666";
                }
                catch (Exception)
                {
                    return "011-411-70666";
                }
            }
        }

        public bool CensusDataRefresh
            => GetBooleanSettingValue("CensusDataRefresh", false);

        public bool UsesAutomatedTADA
            => GetBooleanSettingValue("UsesAutomatedTADA", false);

        public bool CustomCalculationLogicforTA
            => GetBooleanSettingValue("CustomCalculationLogicforTA", false);

        public List<string> NoSalesReasonInPrimaryOrders
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("NoSalesReasonInPrimaryOrders"))
                    {
                        if (_settings["NoSalesReasonInPrimaryOrders"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["NoSalesReasonInPrimaryOrders"];
                        }
                        else if (_settings["NoSalesReasonInPrimaryOrders"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray().GetType())
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["NoSalesReasonInPrimaryOrders"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return new List<string>();
                        }
                    }
                    else
                    {
                        return new List<string>();
                    }
                }
                catch (Exception)
                {
                    return new List<string>();
                }
            }
        }

        public bool CompanyUsesManDaysCapping =>
            GetBooleanSettingValue("CompanyUsesManDaysCapping");

        public string ProductRecommendationSalesConsidered =>
            GetStringSettingValue("ProductRecommendationSalesConsidered", "Order");

        public bool CompanyUsesAdminFlowInAssetAllocation =>
            GetBooleanSettingValue("CompanyUsesAdminFlowInAssetAllocation");

        public bool CompanyUsesTertiaryOrderFlow =>
            GetBooleanSettingValue("CompanyUsesTertiaryOrderFlow");

        public bool CompanyUsesProductVisibilityControlRule =>
            GetBooleanSettingValue("CompanyUsesProductVisibilityControlRule(PVCR)");

        public string EndUserFeedback
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("EndUserFeedback"))
                    {
                        return (string)_settings["EndUserFeedback"];
                    }

                    return "Your feedback helps us understand your needs and make our services better for you.";
                }
                catch (Exception)
                {
                    return "Your feedback helps us understand your needs and make our services better for you.";
                }
            }
        }

        public string UOMPriceVisibilityInTheApplication
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UOMPriceVisibilityInTheApplication"))
                    {
                        return (string)_settings["UOMPriceVisibilityInTheApplication"];
                    }
                    else
                    {
                        return "Value";
                    }
                }
                catch (Exception)
                {
                    return "Value";
                }
            }
        }

        public string CompanyUsesEmailNotification =>
            GetStringSettingValue("CompanyUsesEmailNotification(GT)");

        public bool CompanyUsesFACopilot =>
            GetBooleanSettingValue("CompanyUsesFACopilot");

        public bool ShowMTDBilledTagOnProducts =>
            GetBooleanSettingValue("ShowMTDBilledTagOnProducts");

        public bool CompanyUsesAdvanceLeave =>
            GetBooleanSettingValue("CompanyUsesAdvanceLeave");

        public bool CompanyusesOutletRevisitFeature =>
            GetBooleanSettingValue("CompanyusesOutletRevisitFeature");

        public bool CompanyUsesTaxRecalculation =>
            GetBooleanSettingValue("CompanyUsesTaxRecalculation");

        public bool showTimerOnOrderBooking =>
            GetBooleanSettingValue("showTimerOnOrderBooking");

        public bool ShowPreviousVisits =>
            GetBooleanSettingValue("ShowPreviousVisits");

        public bool CompanyusesEffective_PCKPI =>
            GetBooleanSettingValue("CompanyusesEffective_PCKPI");

        public bool UsesAdvanceDeadOutletFlow =>
            GetBooleanSettingValue("UsesAdvanceDeadOutletFlow");

        public bool FlexibleTargetVisibilityManagerAppOld =>
            GetBooleanSettingValue("FlexibleTargetVisibility-ManagerApp(Old)");

        public bool AllowInvoicingOnDefaultPriceInCustomerCategory =>
            GetBooleanSettingValue("AllowInvoicingOnDefaultPriceInCustomerCategory");

        public bool AssetMappingQRFlow =>
            GetBooleanSettingValue("AssetMappingQRFlow");

        public bool FilterSurveysViaUserCohortInFlutter =>
            GetBooleanSettingValue("FilterSurveysViaUserCohortInFlutter");

        public List<string> GetDistributorStockCapturedAllowedOnDates
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("DistributorStockCapturedAllowedOnDates"))
                    {
                        if (_settings["DistributorStockCapturedAllowedOnDates"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["DistributorStockCapturedAllowedOnDates"];
                        }
                        else if (_settings["DistributorStockCapturedAllowedOnDates"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType())
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["DistributorStockCapturedAllowedOnDates"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public bool AllowBatchWiseOrderWithoutStock =>
           GetBooleanSettingValue("AllowBatchWiseOrderWithoutStock");

        public bool ShowPrimaryOrderInvoices =>
            GetBooleanSettingValue("ShowPrimaryOrderInvoices");

        public bool ShowPrimaryOrderHistory =>
            GetBooleanSettingValue("ShowPrimaryOrderHistory");

        public bool CompanyUsesOutletDuplicateCheckDistributorWise =>
            GetBooleanSettingValue("CompanyUsesOutletDuplicateCheckDistributorWise");

        public List<string> GetNoDeliveryReasonsInDeliveryApp
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("NoDeliveryReasonsInDeliveryApp"))
                    {
                        if (_settings["NoDeliveryReasonsInDeliveryApp"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["NoDeliveryReasonsInDeliveryApp"];
                        }
                        else if (_settings["NoDeliveryReasonsInDeliveryApp"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType())
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["NoDeliveryReasonsInDeliveryApp"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public bool usesVanSales =>
            GetBooleanSettingValue("usesVanSales");

        public string UserFeedbackTriggerDate
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UserFeedbackTriggerDate"))
                    {
                        return (string)_settings["UserFeedbackTriggerDate"];
                    }
                    else
                    {
                        return "10";
                    }
                }
                catch (Exception)
                {
                    return "10";
                }
            }
        }

        public bool CompanyHasDirectDealers => GetBooleanSettingValue("CompanyHasDirectDealers");
        public bool EnableBottlerCompanyFlows => GetBooleanSettingValue("EnableBottlerCompanyFlows");
        public bool DistributorVisitAsDayStartType => GetBooleanSettingValue("DistributorVisitAsDayStartType");
        public bool CompanyUsesProductOutletWiseCueCards => GetBooleanSettingValue("CompanyUsesProductOutletWiseCueCards");
        public bool OrderStatusValidationFromExternalAPI => GetBooleanSettingValue("OrderStatusValidationFromExternalAPI");

        public List<string> GetReasonForExpiredReturn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForExpiredReturn"))
                    {
                        if (_settings["ReasonForExpiredReturn"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonForExpiredReturn"];
                        }
                        else if (_settings["ReasonForExpiredReturn"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonForExpiredReturn"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public List<string> GetReasonForDamagedReturn
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("ReasonForDamagedReturn"))
                    {
                        if (_settings["ReasonForDamagedReturn"].GetType() == new List<string>().GetType())
                        {
                            return (List<string>)_settings["ReasonForDamagedReturn"];
                        }
                        else if (_settings["ReasonForDamagedReturn"].GetType() ==
                                 new Newtonsoft.Json.Linq.JArray()
                                     .GetType()) // needed to load Data from cache saved As Json
                        {
                            return ((Newtonsoft.Json.Linq.JArray)_settings["ReasonForDamagedReturn"])
                                .Select(j => j.ToString()).ToList();
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        public AppLockType CompanyUsesAppLockType
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesAppLockType"))
                    {
                        var appLockType = (string)_settings["CompanyUsesAppLockType"];
                        return appLockType.TestNullAssign(AppLockType.Mpin);
                    }
                    else
                    {
                        return AppLockType.Mpin;
                    }
                }
                catch (Exception)
                {
                    return AppLockType.Mpin;
                }
            }
        }

        public string UserJourneyplancanbe
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("UserJourneyplancanbe"))
                    {
                        return (string)_settings["UserJourneyplancanbe"];
                    }

                    return "Both Repeatable & Non-Repeatable";
                }
                catch (Exception)
                {
                    return "Both Repeatable & Non-Repeatable";
                }
            }
        }

        public bool CompanyUsesCartDraftFunctionality
            => GetBooleanSettingValue("CompanyUsesCartDraftFunctionality");

        public bool CompanyUsesEmpContactNumberUpdation
            => GetBooleanSettingValue("CompanyUsesEmpContactNumberUpdation");

        public bool CompanyUsesDisplayCategoryinAppForSuperCode
            => GetBooleanSettingValue("CompanyUsesDisplayCategoryinAppForSuperCode");

        public bool CompanyusesNetValueinNonFAInvoices
            => GetBooleanSettingValue("CompanyusesNetValueinNonFAInvoices");

        public bool CompanyUsesPackSizeVisibility => GetBooleanSettingValue("CompanyUsesPackSizeVisibility");

        public bool AllowUsersToSeeTentativeStockOffline => GetBooleanSettingValue("AllowUsersToSeeTentativeStockOffline");
        public int SurveyTriggerAfterHours
       => GetIntegerSettingValue("surveytriggerafterhours", 1);
        public bool CompanyUsesTheFlexibleTourPlanOrNot => GetBooleanSettingValue("CompanyUsestheFlexibleTourPlanOrNot");
        public string GetEntityListForFlexibleTourPlan
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CompanyUsesFlexibleTourPlan"))
                    {
                        var mappingType = (string)_settings["CompanyUsesFlexibleTourPlan"];
                        return mappingType;
                    }
                    else
                    {
                        return "User";
                    }
                }
                catch (Exception)
                {
                    return "User";
                }
            }
        }

        public bool CustomNetValueinTodayPerformance => GetBooleanSettingValue("CustomNetValueinTodayPerformance");

        public bool AllowUserToEditPriceInTheInvoicePostDelivery => GetBooleanSettingValue("AllowUserToEditPriceInTheInvoicePostDelivery");

        public bool CompanyUsesOfflineRetailerCollectionModule => GetBooleanSettingValue("CompanyUsesOfflineRetailerCollectionModule");

        public bool SendDistributorIrrespectiveOfToken => GetBooleanSettingValue("SendDistributorIrrespectiveOfToken");

        public bool CompanyUsesAssetAuditWithQRFlow => GetBooleanSettingValue("CompanyUsesAssetAuditWithQRFlow");

        public bool AllowAccessToAllApps => GetBooleanSettingValue("AllowAccessToAllApps");

        public bool RestrictOrderBookingwhereBeatDBMappingisabsent => GetBooleanSettingValue("RestrictOrderBookingwhereBeat-DBMappingisabsent");
        public bool CompanyAllowsPaymentCollectionForPresale => GetBooleanSettingValue("CompanyAllowsPaymentCollectionForPresale");
        public bool CompanyAllowsPaymentCollectionAgainstDelivery => GetBooleanSettingValue("CompanyAllowsPaymentCollectionAgainstDelivery");

        public bool CompanyUsesCreditBalanceForOutstandingCalculation => GetBooleanSettingValue("CompanyUsesCreditBalanceForOutstandingCalculation");

        public bool CompanyUsesPrintingForPreSales => GetBooleanSettingValue("CompanyUsesPrintingForPreSales");

        public bool CompanyUsesTaxVerification => GetBooleanSettingValue("CompanyUsesTaxVerification");

        public bool CompanyWantToSeeTheCollectionHistory => GetBooleanSettingValue("CompanyWantToSeeTheCollectionHistory");

        public bool JwWithBarCode => GetBooleanSettingValue("JwWithBarCode");

        public int MaxRecommendationsOnCopilot
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("MaxRecommendationsOnCopilot"))
                    {
                        return (int)(long)_settings["MaxRecommendationsOnCopilot"];
                    }
                    else
                    {
                        return 5;
                    }
                }
                catch (Exception)
                {
                    return 5;
                }
            }
        }

        public bool ShowLiveDistributorStock => GetBooleanSettingValue("ShowLiveDistributorStock");

        public bool CompanyUsesSharePDFAfterOrderConfirmation => GetBooleanSettingValue("CompanyUsesSharePDFAfterOrderConfirmation");

        public bool CompanyAllowsEmptyManagementInApp => GetBooleanSettingValue("CompanyAllowsEmptyManagementInApp");

        public bool CompanyUsesPricingMaster => GetBooleanSettingValue("CompanyUsesPricingMaster");

        public bool OptimizedFlowForL3AndAbove => GetBooleanSettingValue("OptimizedFlowForL3AndAbove(GTApp)");

        public int MaxDistributorIdsPerRequest =>
            GetIntegerSettingValue("MaxDistributorIdsPerRequest", 5);

        public double IncrementalValueForCashDiscount =>
            GetDoubleSettingValue("IncrementalValueForCashDiscount", 1.0);

        public bool HideProductsWithPTRZeroinApp => GetBooleanSettingValue("HideProductsWithPTRZeroinApp");

        public bool CompanyUserEitherCashORNeftAsModeOfPayment => GetBooleanSettingValue("CompanyUserEitherCashORNeftAsModeOfPayment");

        public int CollectionAllowedForLastNDaysInvoices
        {
            get
            {
                try
                {
                    if (_settings.ContainsKey("CollectionAllowedForLastNDaysInvoices"))
                    {
                        return (int)(long)_settings["CollectionAllowedForLastNDaysInvoices"];
                    }
                    else
                    {
                        return 30;
                    }
                }
                catch (Exception)
                {
                    return 30;
                }
            }
        }

        public bool CompanyUsesRetailerCollection => GetBooleanSettingValue("CompanyUsesRetailerCollection");

        public bool DisableSyncRetailerCollectionsToRetailerLedger => GetBooleanSettingValue("DisableSyncRetailerCollectionsToRetailerLedger");

        public bool CompanyUsesDirectLoadoutFromDMS => GetBooleanSettingValue("DirectLoadoutFromDMS");
    }
}
