﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Library.TransactionDb.DbModels;

public class StockofDayEnd
{
    [Key]
    public long Id { get; set; }

    [ForeignKey("VanDayStock")]
    public long VanDayEndStockId { get; set; }

    public VanDayStock VanDayEndStock { get; set; }

    public long PrimaryCategoryId { get; set; }

    public long SecondaryCategoryId { get; set; }

    public long ProductDivisionId { get; set; }

    public long ProductId { get; set; }

    public double StandardUnitConversionFactor { get; set; }

    public long VansStock { get; set; }

    public long UnloadedStock { get; set; }

    public string Reason { get; set; }

    public Guid Guid { get; set; }

    public double? SaleableReturn { get; set; }

    public double? ExpiredReturn { get; set; }

    public double? DamagedReturn { get; set; }

    public bool? IsEdited { get; set; }

    public string BatchNo { get; set; }

    public DateTime? MfgDate { get; set; }

    public long? OldStock { get; set; }

    public long? FreeQty { get; set; }
}
