﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using HCCB.DbStorage.DbContexts;
using TruMindsQueueProcessor.Services;
using Core.Repositories;
using HCCB.DbStorage.Repositories.HccbRepositories;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Library.StorageWriter.Reader_Writer;
using TruMindsQueueProcessor.Models;
using HCCB.DbStorage.Repositories.MasterRepositories;
using Serilog.Events;
using Serilog;
using Library.SlackService.Helpers;

namespace TruMindsQueueProcessor.Configuration
{
    public static class Dependencies
    {
        public const string ContainerForImages = "ir-images";
        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            // Database Contexts
            services.AddDbContext<WritableHccbDataContext>(options =>
                options.UseSqlServer(config.GetConnectionString("WritableHccbDbConnectionString"), SqlResiliencyBuilder));
            services.AddDbContext<ReadOnlyMasterDbContext>(options =>
            {
                options.UseSqlServer(config.GetConnectionString("ReadonlyMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            var hccbStorageConnectionString = config.GetConnectionString("AzureWebJobsStorage");

            services.AddSlackDirectLogger(config, "Slack:WebHook");

            // HTTP Client Configuration
            services.AddHttpClient("HCCBClient", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(120);
            });
            // TruMinds Config
            services.Configure<TruMindsConfig>(config.GetSection("TruMinds"));

            // Repositories
            services.AddScoped<IMaxerienceLogRepository, MaxerienceLogRepository>();
            services.AddScoped<IFaMasterRepository, FaMasterRepository>();

            // Services
            services.AddTransient<TruMindsQueueProcessorJob>();
            services.AddScoped<TruMindsSyncService>();
            services.AddScoped(d => new HCCBBlobReader(hccbStorageConnectionString, ContainerForImages));
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.CommandTimeout(300);
            optionsBuilder.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        }

        public static void LogSetUp(LoggerConfiguration loggerConfiguration, IConfiguration config)
        {
            loggerConfiguration
                .WriteTo.Console(LogEventLevel.Information);
        }
    }
}