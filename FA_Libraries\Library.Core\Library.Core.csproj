﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
    <PackageReference Include="prometheus-net" Version="8.2.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\fa_dotnet_core\FA.Cache\FA.Cache.csproj" />
    <ProjectReference Include="..\fa_dotnet_logger\FA.Logger\FA.Logger.csproj" />
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
    <ProjectReference Include="..\Library.MasterDb\Library.MasterDb.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Repositories\" />
  </ItemGroup>

</Project>
