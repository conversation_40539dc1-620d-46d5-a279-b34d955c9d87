﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace Library.TransactionDb.DbModels;

[Table("ShipmentVanDayStockMapping")]
public class ShipmentVanDayStockMapping
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    [Required]
    public long ShipmentId { get; set; }

    [Required]
    [ForeignKey("VanDayStock")]
    public long VanDayStockId { get; set; }

    [MaxLength]
    public string InvoiceList { get; set; }
    public RequestStatusCategory Status { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public virtual VanDayStock VanDayStock { get; set; }
}
