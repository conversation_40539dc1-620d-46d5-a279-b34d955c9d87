using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using TruMindsQueueProcessor.Configuration;
using Serilog;

namespace TruMindsQueueProcessor
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((hostingContext, configBuilder) =>
                {
                    configBuilder
                        .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                        .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", optional: true)
                        .AddEnvironmentVariables();
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddAzureStorageQueues(s =>
                    {
#if DEBUG
                        s.BatchSize = 1;
#else
                        s.BatchSize = 16;
#endif
                    });
                    b.AddTimers();
                })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseSerilog((_, loggerConfiguration) =>
                {
                    Dependencies.LogSetUp(loggerConfiguration, _.Configuration);
                })
                .UseConsoleLifetime();

            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }
    }
}