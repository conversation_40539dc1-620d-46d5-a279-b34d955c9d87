pr:
  branches:
    include:
      - hotfix/*
      - main

pool:
  name: "scaleset-agent"

variables:
  buildConfiguration: 'Release'

jobs:
- job: BuildAndTest
  timeoutInMinutes: 60
  pool:
    name: "scaleset-agent"
  steps:
  - checkout: self
    persistCredentials: true
    clean: true
    submodules: recursive
    fetchDepth: 0

  - script: dotnet restore
    displayName: 'dotnet restore'

  - script: dotnet format --verify-no-changes --exclude ./fa_dotnet_clickhouse_connector ./fa_dotnet_core ./fa_dotnet_logger ./CustomAnalyzers --verbosity d
    displayName: 'format check'

  - script: dotnet build --configuration $(buildConfiguration)
    displayName: 'dotnet build'