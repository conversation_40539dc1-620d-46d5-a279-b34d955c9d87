﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Library.Database.Dms.Models;

public class ShipmentItems
{
    public long Id { get; set; }
    public long ProductId { get; set; }
    public DateTime? Mfgdate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public long DeliveredQuantity { get; set; }
    public decimal TotalValue { get; set; }
    public decimal Mrp { get; set; }
    public string SoNo { get; set; }
    public string DeliveryNo { get; set; }
    public string InvoiceNo { get; set; }
    public long BuyerId { get; set; }
    public string BuyerFirmName { get; set; }
    public string BuyerPhoneNumber { get; set; }
    public string BuyerState { get; set; }
    public string BuyerRegion { get; set; }
    public string BuyerShippingAddress { get; set; }
    public string BuyerBillingAddress { get; set; }
    public string BuyerType { get; set; }
    public string BuyerPanNumber { get; set; }
    public string BuyerGstin { get; set; }
    public DateTime CreatedAt { get; set; }
    public long? ShipmentId { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string PrimarySchemeId { get; set; }
    public string PrimarySchemeCode { get; set; }
    public string SecondarySchemeId { get; set; }
    public decimal CashDiscountPercent { get; set; }
    public decimal CashDiscountValue { get; set; }
    public decimal PrimaryDiscountPercent { get; set; }
    public decimal PrimaryDiscountValue { get; set; }
    public decimal SecondaryDiscountPercent { get; set; }
    public decimal SecondaryDiscountValue { get; set; }
    public decimal GrossValue { get; set; }
    public decimal NetValue { get; set; }
    public decimal Cgst { get; set; }
    public decimal Cgstpercentage { get; set; }
    public decimal Cgstvalue { get; set; }
    public decimal Sgst { get; set; }
    public decimal Sgstpercentage { get; set; }
    public decimal Sgstvalue { get; set; }
    public decimal Igst { get; set; }
    public decimal Igstpercentage { get; set; }
    public decimal Igstvalue { get; set; }
    public decimal Utgst { get; set; }
    public decimal Utgstpercentage { get; set; }
    public decimal Utgstvalue { get; set; }
    public decimal GstPercent { get; set; }
    public decimal GstValue { get; set; }
    public decimal VATPercentage { get; set; }
    public decimal VATValue { get; set; }
    public string Name { get; set; }
    public long? Hsncode { get; set; }
    public bool IsBonusProduct { get; set; }
    public decimal Price { get; set; }
    public decimal PriceWithGst { get; set; }
    public string Unit { get; set; }
    public decimal Cases { get; set; }
    public decimal Pieces { get; set; }
    public decimal Quantity { get; set; }
    public long SecondayCategoryId { get; set; }
    public string SecondayCategory { get; set; }
    public long PrimaryCategoryId { get; set; }
    public string PrimaryCategory { get; set; }
    public long ProductDivisionId { get; set; }
    public string ProductDivision { get; set; }
    public string BatchNo { get; set; }
    public decimal RevisedPrice { get; set; }
    public decimal AdditionalDiscountPercent1 { get; set; }
    public decimal AdditionalDiscountValue1 { get; set; }
    public decimal AdditionalDiscountPercent2 { get; set; }
    public decimal AdditionalDiscountValue2 { get; set; }
    public string SecondarySchemeCode { get; set; }
    public bool? IsSchemedProduct { get; set; }
    public decimal TradeDiscountPercent { get; set; }
    public decimal TradeDiscountValue { get; set; }
    public string ProductErpid { get; set; }
    public decimal FreeQty { get; set; }
    public decimal Focpercent { get; set; }
    public decimal Focvalue { get; set; }
    public DateTime InvoiceDate { get; set; }
    public long? BuyerRegionId { get; set; }
    public string BuyerZone { get; set; }
    public long? BuyerZoneId { get; set; }
    public decimal Tcsvalue { get; set; }
    public decimal StandardUnitConversionFactor { get; set; }
    public decimal? UnitWeight { get; set; }
    public decimal TotalWithTcs { get; set; }
    public decimal Cesspercentage { get; set; }
    public decimal Cessvalue { get; set; }
    public decimal Cess { get; set; }
    public long ProductDbId { get; set; }
    public string Mrpbatch { get; set; }
    public string VariantName { get; set; }
    public decimal MrpbillingPrice { get; set; }
    public string SellerErpId { get; set; }
    public string BuyerErpId { get; set; }
    public bool IsInvoiceCancelled { get; set; }
    public string PrimarySchemeSlabId { get; set; }
    public string SecondarySchemeSlabId { get; set; }
    public long? FaorderId { get; set; }
    public long? InvoiceId { get; set; }
    public decimal SuperUnit { get; set; }
    public decimal SuperUnitConversionFactor { get; set; }
    public long LoadInQuantity { get; set; }
    public long InvoicedQuantity { get; set; }
    public long DeltaQuantity { get; set; }
    public long DamagedQuantity { get; set; }

    public virtual Shipment Shipment { get; set; }
}
