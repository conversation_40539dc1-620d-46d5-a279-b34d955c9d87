﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Library.MasterDb.DbModels;

public class AssetMappingDetail
{
    public long Id { get; set; }

    public long AssetDefinitionsId { get; set; }

    public long ValueCapacity { get; set; }

    public long VolumeCapacity { get; set; }

    public string AssetName { get; set; }

    public string AssetType { get; set; }

    public string AssetReferenceNo { get; set; }

    public string AssetState { get; set; }

    public string ImageId { get; set; }

    public long? AssetTypeId { get; set; }

    public long? EquipmentId { get; set; }

    public string EquipmentName { get; set; }
    public bool IsIRAsset { get; set; }
}
