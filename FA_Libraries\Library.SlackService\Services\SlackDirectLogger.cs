// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Library.SlackService.Helpers;
using Library.SlackService.Interfaces;
using Microsoft.Extensions.Hosting;
using Polly;
using Microsoft.Extensions.Logging;
using Library.SlackService.Model;
using Library.CommonHelpers;

namespace Library.SlackService.Services;

public class SlackDirectLogger(IHttpClientFactory httpClientFactory, ILogger<SlackDirectLogger> logger) 
    : BackgroundService, ISlackLogger
{
    private readonly HttpClient _slackWebHookClient = httpClientFactory.CreateClient(SlackLogHelperMethods.SlackWebHookClientName);
    private readonly ILogger<SlackDirectLogger> _logger = logger;
    private readonly BlockingCollection<SlackMessage> _entryQueue = new BlockingCollection<SlackMessage>(1024);
    private readonly AsyncPolicy<HttpResponseMessage> _retryPolicy = Policy
                        .Handle<HttpRequestException>()
                        .OrResult<HttpResponseMessage>(result => result.StatusCode == HttpStatusCode.TooManyRequests)
                        .WaitAndRetryAsync(10, 
                        sleepDurationProvider: (retryAttempt, result) => 
                            {
                                return TimeSpan.FromSeconds(Math.Pow(2, retryAttempt));
                            }, 
                        onRetry: (outcome, timespan, retryCount, context) =>
                            {
                                if(outcome.Result.StatusCode == HttpStatusCode.TooManyRequests)
                                {

                                }
                                logger.LogWarning($"Logging Failed. Retry attempt: {retryCount}\n{outcome.Exception}");
                            });

    void ISlackLogger.LogInfo(string logMessage, object metaData)
    {
        try
        {

            _retryPolicy;
            var slackMessage = SlackLogHelperMethods.GetSlackMessage(logMessage, null, metaData, LogLevel.Information);
            _entryQueue.Add(slackMessage);
        }
        catch (Exception loggingException)
        {
            _logger.LogError($"Failed to add log to queue. Error:\n{loggingException.Message}");
            _logger.LogError($"{logMessage}\n{JsonSerializer.Serialize(metaData)}");
        }
    }

    void ISlackLogger.LogError(string logMessage, Exception ex, object metaData)
    {
        try
        {
            var slackMessage = SlackLogHelperMethods.GetSlackMessage(logMessage, ex, metaData, LogLevel.Error);

            _entryQueue.Add(slackMessage);
        }
        catch (Exception loggingException)
        {
            _logger.LogError($"Failed to add log to queue. Error:\n{loggingException.Message}");
            _logger.LogError($"{logMessage}\n{JsonSerializer.Serialize(metaData)}\n{ex}");
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation($"{GetType().FullName} background service is starting.");

        foreach (var message in _entryQueue.GetConsumingEnumerable(stoppingToken))
        {
            try
            {
                await SendSlackMessageWithRetryAsync(message, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send Slack message after all retries");
            }

            if (stoppingToken.IsCancellationRequested)
            {
                _entryQueue.CompleteAdding();
            }
        }

        _logger.LogError($"Log background service is stopping.");
        _entryQueue.Dispose();
    }

    private async Task SendSlackMessageWithRetryAsync(SlackMessage message, CancellationToken cancellationToken)
    {
        const int MaxRetries = 10;
        var retryCount = 0;

        while (retryCount <= MaxRetries)
        {
            try
            {
                var response = await _slackWebHookClient.PostAsJsonAsync("", message, cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    return; // Success, exit retry loop
                }

                if (response.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    // Handle rate limiting with Retry-After header
                    var retryAfter = GetRetryAfterDelay(response);

                    _logger.LogWarning("Slack rate limit hit. Retry attempt: {RetryCount}. Waiting {WaitSeconds} seconds",
                        retryCount + 1, retryAfter.TotalSeconds);

                    await Task.Delay(retryAfter, cancellationToken);
                    retryCount++;
                    continue;
                }

                // For other non-success status codes, use exponential backoff
                if (retryCount < MaxRetries)
                {
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount + 1));
                    _logger.LogWarning("Slack webhook failed. Status: {StatusCode}. Retry attempt: {RetryCount}. Waiting {WaitSeconds} seconds",
                        response.StatusCode, retryCount + 1, delay.TotalSeconds);

                    await Task.Delay(delay, cancellationToken);
                    retryCount++;
                    continue;
                }

                // Final failure after all retries
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Slack webhook failed after all retries. Status: {StatusCode}, Content: {ResponseContent}",
                    response.StatusCode, responseContent);
                return;
            }
            catch (HttpRequestException ex)
            {
                if (retryCount < MaxRetries)
                {
                    var delay = TimeSpan.FromSeconds(Math.Pow(2, retryCount + 1));
                    _logger.LogWarning(ex, "Slack webhook request failed. Retry attempt: {RetryCount}. Waiting {WaitSeconds} seconds",
                        retryCount + 1, delay.TotalSeconds);

                    await Task.Delay(delay, cancellationToken);
                    retryCount++;
                    continue;
                }

                _logger.LogError(ex, "Slack webhook failed after all retries due to HTTP request exception");
                return;
            }
        }
    }

    private static TimeSpan GetRetryAfterDelay(HttpResponseMessage response)
    {
        // Check for Retry-After header
        if (response.Headers.RetryAfter?.Delta.HasValue == true)
        {
            return response.Headers.RetryAfter.Delta.Value;
        }

        // If Retry-After header has a date instead of delta
        if (response.Headers.RetryAfter?.Date.HasValue == true)
        {
            var retryAfterDate = response.Headers.RetryAfter.Date.Value;
            var delay = retryAfterDate - DateTimeOffset.UtcNow;
            return delay > TimeSpan.Zero ? delay : TimeSpan.FromSeconds(1);
        }

        // Default fallback if no Retry-After header is present
        return TimeSpan.FromSeconds(60);
    }
}
