// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Concurrent;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Library.SlackService.Helpers;
using Library.SlackService.Interfaces;
using Microsoft.Extensions.Hosting;
using Polly;
using Microsoft.Extensions.Logging;
using Library.SlackService.Model;
using Library.CommonHelpers;

namespace Library.SlackService.Services;

public class SlackDirectLogger(IHttpClientFactory httpClientFactory, ILogger<SlackDirectLogger> logger) 
    : BackgroundService, ISlackLogger
{
    private readonly HttpClient _slackWebHookClient = httpClientFactory.CreateClient(SlackLogHelperMethods.SlackWebHookClientName);
    private readonly ILogger<SlackDirectLogger> _logger = logger;
    private readonly BlockingCollection<SlackMessage> _entryQueue = new BlockingCollection<SlackMessage>(1024);
    private readonly AsyncPolicy _retryPolicy = Policy
                        .Handle<HttpRequestException>()
                        .WaitAndRetryAsync(10, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), onRetry: (exception, timespan, retryCount, context) =>
                        {
                            logger.LogWarning($"Logging Failed. Retry attempt: {retryCount}\n{exception}");
                        });

    void ISlackLogger.LogInfo(string logMessage, object metaData)
    {
        try
        {
            var slackMessage = SlackLogHelperMethods.GetSlackMessage(logMessage, null, metaData, LogLevel.Information);
            _entryQueue.Add(slackMessage);
        }
        catch (Exception loggingException)
        {
            _logger.LogError($"Failed to add log to queue. Error:\n{loggingException.Message}");
            _logger.LogError($"{logMessage}\n{JsonSerializer.Serialize(metaData)}");
        }
    }

    void ISlackLogger.LogError(string logMessage, Exception ex, object metaData)
    {
        try
        {
            var slackMessage = SlackLogHelperMethods.GetSlackMessage(logMessage, ex, metaData, LogLevel.Error);

            _entryQueue.Add(slackMessage);
        }
        catch (Exception loggingException)
        {
            _logger.LogError($"Failed to add log to queue. Error:\n{loggingException.Message}");
            _logger.LogError($"{logMessage}\n{JsonSerializer.Serialize(metaData)}\n{ex}");
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation($"{GetType().FullName} background service is starting.");

        foreach (var message in _entryQueue.GetConsumingEnumerable())
        {
            try
            {
                await _retryPolicy.ExecuteAsync(async () =>
                    {
                        await (await _slackWebHookClient.PostAsJsonAsync("", message)).EnsureSuccessStatusCodeAsync();
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(exception: ex, message: ex.Message);
            }

            if (stoppingToken.IsCancellationRequested)
            {
                _entryQueue.CompleteAdding();
            }
        }

        _logger.LogError($"Log background service is stopping.");
        _entryQueue.Dispose();
    }
}
